// GolfCourseAPIClient.ts
// XANO-integrated API client for golf course management
import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';

// --- Type Definitions for XANO Integration ---

export interface ApiConfig {
  baseURL: string;
  timeout: number;
  headers: {
    'Content-Type': string;
    'Authorization'?: string;
  };
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: ApiError;
  pagination?: PaginationInfo;
  meta?: ResponseMeta;
}

export interface ApiError {
  code: string;
  message: string;
  details?: any;
  field_errors?: FieldError[];
}

export interface FieldError {
  field: string;
  message: string;
  code: string;
}

export interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  total_pages: number;
  has_next: boolean;
  has_previous: boolean;
}

export interface ResponseMeta {
  timestamp: string;
  request_id: string;
  version: string;
}

// XANO Golfer Types (matching database schema)
export interface XanoGolfer {
  id: number;
  created_at: string;
  global_id?: string;
  crm_golfer_id?: string;
  first_name: string;
  last_name: string;
  email: string;
  phone_number?: string;
  address?: string;
  city?: string;
  state?: string;
  zip_code?: string;
  country?: string;
  handicap?: number;
  upcoming_tee_time?: string;
  past_tee_times?: string[];
  past_food_orders?: string[];
  backfill_presented?: boolean;
  backfill_accepted?: boolean;
  waitlist_enabled?: boolean;
  waitlist_dates_times?: string[];
  preferred_play_day_times?: string[];
  preferred_course?: string;
  sms_notifications_enabled?: boolean;
  email_notifications_enabled?: boolean;
  in_app_notifications_enabled?: boolean;
  preferred_table_location?: string;
  payment_info_verified?: boolean;
  campaign_progress?: any[];
  golfer_shipping_address_id?: string;
}

export interface CreateGolferRequest {
  first_name: string;
  last_name: string;
  email: string;
  phone_number?: string;
  address?: string;
  city?: string;
  state?: string;
  zip_code?: string;
  country?: string;
  handicap?: number;
  sms_notifications_enabled?: boolean;
  email_notifications_enabled?: boolean;
  in_app_notifications_enabled?: boolean;
  preferred_table_location?: string;
}

export interface UpdateGolferRequest extends Partial<CreateGolferRequest> {
  waitlist_enabled?: boolean;
  preferred_play_day_times?: string[];
  preferred_course?: string;
}

export interface BaseQueryParams {
  page?: number;
  limit?: number;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
  search?: string;
}

export interface GolferQueryParams extends BaseQueryParams {
  golf_course_id?: string;
  membership_status?: string;
  handicap_range?: [number, number];
}

const DEFAULT_CONFIG: ApiConfig = {
  baseURL: 'https://x8ki-letl-twmt.n7.xano.io/api:uP98JIiJ',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
};

export class GolfCourseAPIClient {
  private config: ApiConfig;
  private axiosInstance;

  constructor(config: Partial<ApiConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.axiosInstance = axios.create({
      baseURL: this.config.baseURL,
      timeout: this.config.timeout,
      headers: this.config.headers,
    });
  }

  setAuthToken(token: string) {
    this.config.headers.Authorization = `Bearer ${token}`;
    this.axiosInstance.defaults.headers['Authorization'] = `Bearer ${token}`;
  }

  clearAuthToken() {
    delete this.config.headers.Authorization;
    delete this.axiosInstance.defaults.headers['Authorization'];
  }

  // --- Core request method ---
  async request<T>(
    method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH',
    endpoint: string,
    options: {
      data?: any;
      params?: Record<string, any>;
      headers?: Record<string, string>;
      timeout?: number;
    } = {}
  ): Promise<ApiResponse<T>> {
    try {
      const response: AxiosResponse = await this.axiosInstance.request({
        method,
        url: endpoint,
        data: options.data,
        params: options.params,
        headers: options.headers,
        timeout: options.timeout,
      });

      // Handle XANO response format
      // XANO typically returns data directly, not wrapped in a response object
      const responseData = response.data;

      return {
        success: true,
        data: responseData,
        meta: {
          timestamp: new Date().toISOString(),
          request_id: response.headers['x-request-id'] || Math.random().toString(36),
          version: response.headers['x-api-version'] || '1.0',
        },
      };
    } catch (error: any) {
      console.error(`API Error [${method} ${endpoint}]:`, error);
      console.error('Error details:', {
        message: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        url: error.config?.url,
        fullURL: `${this.config.baseURL}${endpoint}`
      });
      return {
        success: false,
        error: this.handleApiError(error),
      };
    }
  }

  // --- Auth Endpoints ---
  async login(data: any): Promise<ApiResponse<any>> {
    return this.request('POST', '/auth/login', { data });
  }
  async refresh(data: any): Promise<ApiResponse<any>> {
    return this.request('POST', '/auth/refresh', { data });
  }
  async logout(): Promise<ApiResponse<any>> {
    return this.request('POST', '/auth/logout');
  }
  async forgotPassword(data: any): Promise<ApiResponse<any>> {
    return this.request('POST', '/auth/forgot-password', { data });
  }
  async resetPassword(data: any): Promise<ApiResponse<any>> {
    return this.request('POST', '/auth/reset-password', { data });
  }
  async verifyToken(): Promise<ApiResponse<any>> {
    return this.request('GET', '/auth/verify-token');
  }

  // --- User Endpoints ---
  async getUsers(params: Record<string, any> = {}): Promise<ApiResponse<any>> {
    return this.request('GET', '/users', { params });
  }
  async getUser(id: string): Promise<ApiResponse<any>> {
    return this.request('GET', `/users/${id}`);
  }
  async createUser(data: any): Promise<ApiResponse<any>> {
    return this.request('POST', '/users', { data });
  }
  async updateUser(id: string, data: any): Promise<ApiResponse<any>> {
    return this.request('PUT', `/users/${id}`, { data });
  }
  async deleteUser(id: string): Promise<ApiResponse<any>> {
    return this.request('DELETE', `/users/${id}`);
  }
  async updateUserPermissions(id: string, permissions: any): Promise<ApiResponse<any>> {
    return this.request('POST', `/users/${id}/permissions`, { data: permissions });
  }

  // --- XANO Connection Test ---
  async testConnection(): Promise<ApiResponse<any>> {
    try {
      console.log('Testing XANO connection...');
      console.log('Base URL:', this.config.baseURL);

      // Try to hit a basic endpoint to test connectivity
      const response = await this.request<any>('GET', '/');
      console.log('XANO Connection Test Response:', response);
      return response;
    } catch (error) {
      console.error('XANO Connection Test Failed:', error);
      return {
        success: false,
        error: this.handleApiError(error)
      };
    }
  }

  // --- XANO Golfer Endpoints ---
  async getGolfers(params: GolferQueryParams = {}): Promise<ApiResponse<XanoGolfer[]>> {
    try {
      console.log('🔍 Testing XANO connection and endpoints...');
      console.log('Base URL:', this.config.baseURL);

      // Try the main endpoint first
      const response = await this.request<XanoGolfer[]>('GET', '/golfer', { params });
      console.log('✅ Successfully fetched golfers:', response);
      return response;
    } catch (error: any) {
      console.error('❌ Failed to fetch golfers:', error);

      // Check if it's a 404 (endpoint not found)
      if (error.response?.status === 404) {
        console.log('🔧 Endpoint not found - XANO table/endpoint needs to be created');
        return {
          success: false,
          error: {
            code: 'ENDPOINT_NOT_FOUND',
            message: 'XANO golfer table or API endpoint not found. Please create the golfer table and API endpoints in your XANO dashboard.'
          }
        };
      }

      return {
        success: false,
        error: this.handleApiError(error)
      };
    }
  }

  async getGolfer(id: string): Promise<ApiResponse<XanoGolfer>> {
    if (!id) {
      return {
        success: false,
        error: { code: 'VALIDATION_ERROR', message: 'Golfer ID is required' }
      };
    }
    return this.request<XanoGolfer>('GET', `/golfer/${id}`);
  }

  async createGolfer(data: CreateGolferRequest): Promise<ApiResponse<XanoGolfer>> {
    if (!data.first_name || !data.last_name || !data.email) {
      return {
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'First name, last name, and email are required',
          field_errors: [
            ...(data.first_name ? [] : [{ field: 'first_name', message: 'First name is required', code: 'REQUIRED' }]),
            ...(data.last_name ? [] : [{ field: 'last_name', message: 'Last name is required', code: 'REQUIRED' }]),
            ...(data.email ? [] : [{ field: 'email', message: 'Email is required', code: 'REQUIRED' }])
          ]
        }
      };
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(data.email)) {
      return {
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid email format',
          field_errors: [{ field: 'email', message: 'Invalid email format', code: 'INVALID_FORMAT' }]
        }
      };
    }

    return this.request<XanoGolfer>('POST', '/golfer', { data });
  }

  async updateGolfer(id: string, data: UpdateGolferRequest): Promise<ApiResponse<XanoGolfer>> {
    if (!id) {
      return {
        success: false,
        error: { code: 'VALIDATION_ERROR', message: 'Golfer ID is required' }
      };
    }

    // Validate email format if provided
    if (data.email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(data.email)) {
        return {
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Invalid email format',
            field_errors: [{ field: 'email', message: 'Invalid email format', code: 'INVALID_FORMAT' }]
          }
        };
      }
    }

    return this.request<XanoGolfer>('PATCH', `/golfer/${id}`, { data });
  }

  async deleteGolfer(id: string): Promise<ApiResponse<void>> {
    if (!id) {
      return {
        success: false,
        error: { code: 'VALIDATION_ERROR', message: 'Golfer ID is required' }
      };
    }
    return this.request<void>('DELETE', `/golfer/${id}`);
  }

  async getGolferTeeTimes(id: string, params: Record<string, any> = {}): Promise<ApiResponse<any>> {
    return this.request('GET', `/golfer/${id}/tee-times`, { params });
  }

  async getGolferTransactions(id: string, params: Record<string, any> = {}): Promise<ApiResponse<any>> {
    return this.request('GET', `/golfer/${id}/transactions`, { params });
  }

  async addGolferFunds(id: string, data: any): Promise<ApiResponse<any>> {
    return this.request('POST', `/golfer/${id}/funds`, { data });
  }

  // --- Tee Time Endpoints ---
  async getTeeTimes(params: Record<string, any> = {}): Promise<ApiResponse<any>> {
    return this.request('GET', '/tee-times', { params });
  }
  async getTeeTimeAvailability(params: Record<string, any>): Promise<ApiResponse<any>> {
    return this.request('GET', '/tee-times/availability', { params });
  }
  async bookTeeTime(data: any): Promise<ApiResponse<any>> {
    return this.request('POST', '/tee-times/book', { data });
  }
  async cancelTeeTime(id: string, reason?: string): Promise<ApiResponse<any>> {
    return this.request('PUT', `/tee-times/${id}/cancel`, { data: { reason } });
  }
  async modifyTeeTime(id: string, data: any): Promise<ApiResponse<any>> {
    return this.request('POST', `/tee-times/${id}/modify`, { data });
  }
  async getWaitlist(golf_course_id: string, date?: string): Promise<ApiResponse<any>> {
    return this.request('GET', '/tee-times/waitlist', { params: { golf_course_id, date } });
  }
  async joinWaitlist(data: any): Promise<ApiResponse<any>> {
    return this.request('POST', '/tee-times/waitlist', { data });
  }

  // --- Pro Shop Endpoints ---
  async getProShopItems(params: Record<string, any> = {}): Promise<ApiResponse<any>> {
    return this.request('GET', '/pro-shop/items', { params });
  }
  async getProShopItem(id: string): Promise<ApiResponse<any>> {
    return this.request('GET', `/pro-shop/items/${id}`);
  }
  async createProShopItem(data: any): Promise<ApiResponse<any>> {
    return this.request('POST', '/pro-shop/items', { data });
  }
  async updateProShopItem(id: string, data: any): Promise<ApiResponse<any>> {
    return this.request('PUT', `/pro-shop/items/${id}`, { data });
  }
  async purchaseProShopItems(data: any): Promise<ApiResponse<any>> {
    return this.request('POST', '/pro-shop/purchase', { data });
  }
  async getProShopInventoryReport(golf_course_id: string): Promise<ApiResponse<any>> {
    return this.request('GET', '/pro-shop/inventory', { params: { golf_course_id } });
  }
  async updateProShopInventory(id: string, data: any): Promise<ApiResponse<any>> {
    return this.request('PUT', `/pro-shop/items/${id}/inventory`, { data });
  }

  // --- Food & Beverage Endpoints ---
  async getFoodBeverageItems(params: Record<string, any> = {}): Promise<ApiResponse<any>> {
    return this.request('GET', '/food-beverage/items', { params });
  }
  async createFoodOrder(data: any): Promise<ApiResponse<any>> {
    return this.request('POST', '/food-beverage/order', { data });
  }
  async getFoodOrders(params: Record<string, any> = {}): Promise<ApiResponse<any>> {
    return this.request('GET', '/food-beverage/orders', { params });
  }
  async updateFoodOrderStatus(id: string, status: string, employee_id?: string): Promise<ApiResponse<any>> {
    return this.request('PUT', `/food-beverage/orders/${id}/status`, { data: { status, employee_id } });
  }
  async getFoodMenu(golf_course_id: string): Promise<ApiResponse<any>> {
    return this.request('GET', '/food-beverage/menu', { params: { golf_course_id } });
  }

  // --- Analytics Endpoints ---
  async getRevenueReport(params: Record<string, any>): Promise<ApiResponse<any>> {
    return this.request('GET', '/analytics/revenue', { params });
  }
  async getTeeTimeAnalytics(params: Record<string, any>): Promise<ApiResponse<any>> {
    return this.request('GET', '/analytics/tee-times', { params });
  }
  async getCustomerInsights(golf_course_id: string): Promise<ApiResponse<any>> {
    return this.request('GET', '/analytics/customer-insights', { params: { golf_course_id } });
  }
  async generateCustomReport(data: any): Promise<ApiResponse<any>> {
    return this.request('POST', '/analytics/custom-report', { data });
  }

  // --- Error Handling ---
  handleApiError(error: any): ApiError {
    if (error.response) {
      const status = error.response.status;
      const data = error.response.data;

      // Handle XANO-specific error formats
      if (status === 401) {
        return {
          code: 'UNAUTHORIZED',
          message: 'Authentication required or token expired',
          details: data
        };
      }

      if (status === 403) {
        return {
          code: 'FORBIDDEN',
          message: 'Insufficient permissions',
          details: data
        };
      }

      if (status === 404) {
        return {
          code: 'NOT_FOUND',
          message: 'Resource not found',
          details: data
        };
      }

      if (status === 422) {
        return {
          code: 'VALIDATION_ERROR',
          message: data?.message || 'Validation failed',
          details: data,
          field_errors: data?.errors || []
        };
      }

      return {
        code: data?.code || `HTTP_${status}`,
        message: data?.message || error.response.statusText || 'An API error occurred',
        details: data,
      };
    } else if (error.request) {
      return {
        code: 'NETWORK_ERROR',
        message: 'Network error occurred - please check your connection',
      };
    } else {
      return {
        code: 'UNKNOWN_ERROR',
        message: error.message || 'An unknown error occurred',
      };
    }
  }

  // --- Add more modules/methods as needed, following the spec ---
}

// --- Usage Example ---
// const apiClient = new GolfCourseAPIClient({ baseURL: 'https://api.golfcourse.com/v1' });
// apiClient.setAuthToken('your-jwt-token');
// const users = await apiClient.getUsers(); 