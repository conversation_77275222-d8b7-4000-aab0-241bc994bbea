import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { GolfCourseAPIClient, CreateGolferRequest, UpdateGolferRequest, GolferQueryParams } from '../GolfCourseAPIClient';
import { convertXanoToFrontend, convertFrontendToXano, GolferFormData } from '../../components/Roster/types';

const apiClient = new GolfCourseAPIClient();

// Hook to fetch all golfers with XANO integration
export const useGolfers = (params?: GolferQueryParams) => {
  return useQuery({
    queryKey: ['golfers', params],
    queryFn: async () => {
      const response = await apiClient.getGolfers(params);
      if (!response.success) {
        throw new Error(response.error?.message || 'Failed to fetch golfers');
      }

      // Convert XANO golfers to frontend format
      const frontendGolfers = response.data?.map(convertXanoToFrontend) || [];
      return frontendGolfers;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: (failureCount, error) => {
      // Don't retry on authentication errors
      if (error.message?.includes('Authentication') || error.message?.includes('401')) {
        return false;
      }
      return failureCount < 3;
    },
  });
};

// Hook to fetch a single golfer
export const useGolfer = (id: string) => {
  return useQuery({
    queryKey: ['golfer', id],
    queryFn: async () => {
      if (!id) throw new Error('Golfer ID is required');

      const response = await apiClient.getGolfer(id);
      if (!response.success) {
        throw new Error(response.error?.message || 'Failed to fetch golfer');
      }

      return response.data ? convertXanoToFrontend(response.data) : null;
    },
    enabled: !!id,
    retry: (failureCount, error) => {
      if (error.message?.includes('not found') || error.message?.includes('404')) {
        return false;
      }
      return failureCount < 3;
    },
  });
};

// Hook to create a new golfer
export const useCreateGolfer = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: GolferFormData) => {
      const xanoData = convertFrontendToXano(data);
      const response = await apiClient.createGolfer(xanoData as CreateGolferRequest);

      if (!response.success) {
        throw new Error(response.error?.message || 'Failed to create golfer');
      }

      return response.data ? convertXanoToFrontend(response.data) : null;
    },
    onSuccess: () => {
      // Invalidate and refetch golfers list
      queryClient.invalidateQueries({ queryKey: ['golfers'] });
    },
    onError: (error) => {
      console.error('Failed to create golfer:', error);
    },
  });
};

// Hook to update an existing golfer
export const useUpdateGolfer = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: Partial<GolferFormData> }) => {
      const xanoData = convertFrontendToXano(data as GolferFormData);
      const response = await apiClient.updateGolfer(id, xanoData as UpdateGolferRequest);

      if (!response.success) {
        throw new Error(response.error?.message || 'Failed to update golfer');
      }

      return response.data ? convertXanoToFrontend(response.data) : null;
    },
    onSuccess: (_, { id }) => {
      // Invalidate and refetch both the golfers list and the specific golfer
      queryClient.invalidateQueries({ queryKey: ['golfers'] });
      queryClient.invalidateQueries({ queryKey: ['golfer', id] });
    },
    onError: (error) => {
      console.error('Failed to update golfer:', error);
    },
  });
};

// Hook to delete a golfer
export const useDeleteGolfer = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      const response = await apiClient.deleteGolfer(id);

      if (!response.success) {
        throw new Error(response.error?.message || 'Failed to delete golfer');
      }

      return { id };
    },
    onSuccess: (_, id) => {
      // Remove the golfer from cache and invalidate the list
      queryClient.removeQueries({ queryKey: ['golfer', id] });
      queryClient.invalidateQueries({ queryKey: ['golfers'] });
    },
    onError: (error) => {
      console.error('Failed to delete golfer:', error);
    },
  });
};