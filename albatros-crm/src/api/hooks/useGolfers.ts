import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { GolfCourseAPIClient, CreateGolferRequest, UpdateGolferRequest, GolferQueryParams } from '../GolfCourseAPIClient';
import { convertXanoToFrontend, convertFrontendToXano, GolferFormData } from '../../components/Roster/types';

const apiClient = new GolfCourseAPIClient();



// Hook to fetch all golfers with XANO integration
export const useGolfers = (params?: GolferQueryParams) => {
  return useQuery({
    queryKey: ['golfers', params],
    queryFn: async () => {
      console.log('Fetching golfers from XANO...');

      const response = await apiClient.getGolfers(params);
      console.log('XANO response:', response);

      if (!response.success) {
        throw new Error(response.error?.message || 'Failed to fetch golfers from XANO');
      }

      // Convert XANO golfers to frontend format
      const frontendGolfers = response.data?.map(convertXanoToFrontend) || [];
      console.log('Converted golfers:', frontendGolfers);

      return frontendGolfers;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: (failureCount, error) => {
      // Don't retry on authentication errors
      if (error.message?.includes('Authentication') || error.message?.includes('401')) {
        return false;
      }
      return failureCount < 3;
    },
  });
};

// Hook to fetch a single golfer
export const useGolfer = (id: string) => {
  return useQuery({
    queryKey: ['golfer', id],
    queryFn: async () => {
      if (!id) throw new Error('Golfer ID is required');

      const response = await apiClient.getGolfer(id);
      if (!response.success) {
        throw new Error(response.error?.message || 'Failed to fetch golfer');
      }

      return response.data ? convertXanoToFrontend(response.data) : null;
    },
    enabled: !!id,
    retry: (failureCount, error) => {
      if (error.message?.includes('not found') || error.message?.includes('404')) {
        return false;
      }
      return failureCount < 3;
    },
  });
};

// Hook to create a new golfer (XANO integration - no fallback)
export const useCreateGolfer = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: GolferFormData) => {
      console.log('Creating golfer with data:', data);

      const xanoData = convertFrontendToXano(data);
      console.log('Converted to XANO format:', xanoData);

      const response = await apiClient.createGolfer(xanoData as CreateGolferRequest);
      console.log('XANO response:', response);

      if (!response.success) {
        throw new Error(response.error?.message || 'Failed to create golfer in XANO');
      }

      if (!response.data) {
        throw new Error('No data returned from XANO after creating golfer');
      }

      const createdGolfer = convertXanoToFrontend(response.data);
      console.log('Created golfer (converted back):', createdGolfer);

      return createdGolfer;
    },
    onSuccess: (createdGolfer) => {
      console.log('Successfully created golfer:', createdGolfer);
      // Invalidate and refetch golfers list to show the new golfer
      queryClient.invalidateQueries({ queryKey: ['golfers'] });
    },
    onError: (error) => {
      console.error('Failed to create golfer:', error);
    },
  });
};

// Hook to update an existing golfer (XANO integration - no fallback)
export const useUpdateGolfer = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: Partial<GolferFormData> }) => {
      console.log('Updating golfer with ID:', id, 'Data:', data);

      const xanoData = convertFrontendToXano(data as GolferFormData);
      console.log('Converted to XANO format:', xanoData);

      const response = await apiClient.updateGolfer(id, xanoData as UpdateGolferRequest);
      console.log('XANO update response:', response);

      if (!response.success) {
        throw new Error(response.error?.message || 'Failed to update golfer in XANO');
      }

      const updatedGolfer = response.data ? convertXanoToFrontend(response.data) : null;
      console.log('Updated golfer (converted back):', updatedGolfer);

      return updatedGolfer;
    },
    onSuccess: (updatedGolfer, { id }) => {
      console.log('Successfully updated golfer:', updatedGolfer);
      // Invalidate and refetch both the golfers list and the specific golfer
      queryClient.invalidateQueries({ queryKey: ['golfers'] });
      queryClient.invalidateQueries({ queryKey: ['golfer', id] });
    },
    onError: (error) => {
      console.error('Failed to update golfer:', error);
    },
  });
};

// Hook to delete a golfer (XANO integration - no fallback)
export const useDeleteGolfer = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      console.log('Deleting golfer with ID:', id);

      const response = await apiClient.deleteGolfer(id);
      console.log('XANO delete response:', response);

      if (!response.success) {
        throw new Error(response.error?.message || 'Failed to delete golfer from XANO');
      }

      return { id };
    },
    onSuccess: (_, id) => {
      console.log('Successfully deleted golfer with ID:', id);
      // Remove the golfer from cache and invalidate the list
      queryClient.removeQueries({ queryKey: ['golfer', id] });
      queryClient.invalidateQueries({ queryKey: ['golfers'] });
    },
    onError: (error) => {
      console.error('Failed to delete golfer:', error);
    },
  });
};