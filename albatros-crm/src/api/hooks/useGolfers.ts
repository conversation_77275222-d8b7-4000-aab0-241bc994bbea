import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { GolfCourseAPIClient, CreateGolferRequest, UpdateGolferRequest, GolferQueryParams } from '../GolfCourseAPIClient';
import { convertXanoToFrontend, convertFrontendToXano, GolferFormData, Golfer } from '../../components/Roster/types';

const apiClient = new GolfCourseAPIClient();

// Dummy data for demonstration while XANO is being set up
const dummyGolfers: Golfer[] = [
  {
    id: '1',
    name: '<PERSON>',
    phone: '+****************',
    email: '<EMAIL>',
    stars: 5,
    isMember: true,
    lastPlayDate: '2024-01-15',
    upcomingPlayDate: '2024-01-22',
    address: '123 Golf Lane, Scottsdale, AZ',
    albatrossStarScore: '92',
    nps: '9',
    events: 'Club Championship',
    foodDrink: 'Prefers craft beer',
    student: 'Advanced',
    initials: '<PERSON><PERSON>',
    avatar: '',
    avatarColor: '#1976d2'
  },
  {
    id: '2',
    name: '<PERSON>',
    phone: '+****************',
    email: '<EMAIL>',
    stars: 4,
    isMember: true,
    lastPlayDate: '2024-01-10',
    upcomingPlayDate: '2024-01-25',
    address: '456 Fairway Dr, Phoenix, AZ',
    albatrossStarScore: '87',
    nps: '8',
    events: 'Ladies League',
    foodDrink: 'Vegetarian options',
    student: 'Intermediate',
    initials: 'SJ',
    avatar: '',
    avatarColor: '#d32f2f'
  },
  {
    id: '3',
    name: 'Mike Rodriguez',
    phone: '+****************',
    email: '<EMAIL>',
    stars: 3,
    isMember: false,
    lastPlayDate: '2024-01-08',
    upcomingPlayDate: 'NONE',
    address: '789 Green St, Tempe, AZ',
    albatrossStarScore: '95',
    nps: '7',
    events: 'Weekend Tournaments',
    foodDrink: 'Enjoys wine selection',
    student: 'Beginner',
    initials: 'MR',
    avatar: '',
    avatarColor: '#388e3c'
  },
  {
    id: '4',
    name: 'Emily Chen',
    phone: '+****************',
    email: '<EMAIL>',
    stars: 5,
    isMember: true,
    lastPlayDate: '2024-01-12',
    upcomingPlayDate: '2024-01-20',
    address: '321 Birdie Blvd, Mesa, AZ',
    albatrossStarScore: '84',
    nps: '10',
    events: 'Pro-Am Events',
    foodDrink: 'Gluten-free options',
    student: 'Advanced',
    initials: 'EC',
    avatar: '',
    avatarColor: '#7b1fa2'
  },
  {
    id: '5',
    name: 'David Wilson',
    phone: '+****************',
    email: '<EMAIL>',
    stars: 4,
    isMember: true,
    lastPlayDate: '2024-01-05',
    upcomingPlayDate: '2024-01-28',
    address: '654 Eagle Way, Chandler, AZ',
    albatrossStarScore: '89',
    nps: '8',
    events: 'Corporate Events',
    foodDrink: 'Prefers light meals',
    student: 'Intermediate',
    initials: 'DW',
    avatar: '',
    avatarColor: '#f57c00'
  },
  {
    id: '6',
    name: 'Lisa Thompson',
    phone: '+****************',
    email: '<EMAIL>',
    stars: 3,
    isMember: false,
    lastPlayDate: '2024-01-03',
    upcomingPlayDate: 'NONE',
    address: '987 Par Ave, Glendale, AZ',
    albatrossStarScore: '98',
    nps: '6',
    events: 'Charity Tournaments',
    foodDrink: 'Enjoys cocktails',
    student: 'Beginner',
    initials: 'LT',
    avatar: '',
    avatarColor: '#00796b'
  }
];

// Hook to fetch all golfers with XANO integration (with dummy data fallback)
export const useGolfers = (params?: GolferQueryParams) => {
  return useQuery({
    queryKey: ['golfers', params],
    queryFn: async () => {
      try {
        const response = await apiClient.getGolfers(params);
        if (!response.success) {
          console.warn('XANO API failed, using dummy data:', response.error?.message);
          return dummyGolfers;
        }

        // Convert XANO golfers to frontend format
        const frontendGolfers = response.data?.map(convertXanoToFrontend) || [];
        return frontendGolfers;
      } catch (error: any) {
        console.warn('XANO connection failed, using dummy data:', error.message);
        // Return dummy data instead of throwing error
        return dummyGolfers;
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: false, // Don't retry, just use dummy data
  });
};

// Hook to fetch a single golfer
export const useGolfer = (id: string) => {
  return useQuery({
    queryKey: ['golfer', id],
    queryFn: async () => {
      if (!id) throw new Error('Golfer ID is required');

      const response = await apiClient.getGolfer(id);
      if (!response.success) {
        throw new Error(response.error?.message || 'Failed to fetch golfer');
      }

      return response.data ? convertXanoToFrontend(response.data) : null;
    },
    enabled: !!id,
    retry: (failureCount, error) => {
      if (error.message?.includes('not found') || error.message?.includes('404')) {
        return false;
      }
      return failureCount < 3;
    },
  });
};

// Hook to create a new golfer (with dummy data fallback)
export const useCreateGolfer = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: GolferFormData) => {
      try {
        const xanoData = convertFrontendToXano(data);
        const response = await apiClient.createGolfer(xanoData as CreateGolferRequest);

        if (!response.success) {
          console.warn('XANO create failed, simulating success with dummy data');
          // Simulate successful creation with dummy data
          const newGolfer: Golfer = {
            id: Math.random().toString(36).substring(2, 11),
            name: `${data.firstName} ${data.lastName}`,
            phone: data.phone || '',
            email: data.email || '',
            stars: data.stars || 0,
            isMember: data.isMember || false,
            lastPlayDate: data.lastSeen || '',
            upcomingPlayDate: data.upcomingPlay || 'NONE',
            address: data.address || '',
            albatrossStarScore: data.albatrossStarScore || '0',
            nps: data.nps || '0',
            events: data.events || '',
            foodDrink: data.foodDrink || '',
            student: data.student || '',
            initials: `${data.firstName?.[0] || ''}${data.lastName?.[0] || ''}`,
            avatar: data.avatar || '',
            avatarColor: data.avatarColor || '#1976d2'
          };
          return newGolfer;
        }

        return response.data ? convertXanoToFrontend(response.data) : null;
      } catch (error: any) {
        console.warn('XANO connection failed during create, simulating success');
        // Simulate successful creation
        const newGolfer: Golfer = {
          id: Math.random().toString(36).substring(2, 11),
          name: `${data.firstName} ${data.lastName}`,
          phone: data.phone || '',
          email: data.email || '',
          stars: data.stars || 0,
          isMember: data.isMember || false,
          lastPlayDate: data.lastSeen || '',
          upcomingPlayDate: data.upcomingPlay || 'NONE',
          address: data.address || '',
          albatrossStarScore: data.albatrossStarScore || '0',
          nps: data.nps || '0',
          events: data.events || '',
          foodDrink: data.foodDrink || '',
          student: data.student || '',
          initials: `${data.firstName?.[0] || ''}${data.lastName?.[0] || ''}`,
          avatar: data.avatar || '',
          avatarColor: data.avatarColor || '#1976d2'
        };
        return newGolfer;
      }
    },
    onSuccess: () => {
      // Invalidate and refetch golfers list
      queryClient.invalidateQueries({ queryKey: ['golfers'] });
    },
    onError: (error) => {
      console.error('Failed to create golfer:', error);
    },
  });
};

// Hook to update an existing golfer (with dummy data fallback)
export const useUpdateGolfer = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: Partial<GolferFormData> }) => {
      try {
        const xanoData = convertFrontendToXano(data as GolferFormData);
        const response = await apiClient.updateGolfer(id, xanoData as UpdateGolferRequest);

        if (!response.success) {
          console.warn('XANO update failed, simulating success');
          return null; // Simulate success
        }

        return response.data ? convertXanoToFrontend(response.data) : null;
      } catch (error: any) {
        console.warn('XANO connection failed during update, simulating success');
        return null; // Simulate success
      }
    },
    onSuccess: (_, { id }) => {
      // Invalidate and refetch both the golfers list and the specific golfer
      queryClient.invalidateQueries({ queryKey: ['golfers'] });
      queryClient.invalidateQueries({ queryKey: ['golfer', id] });
    },
    onError: (error) => {
      console.error('Failed to update golfer:', error);
    },
  });
};

// Hook to delete a golfer (with dummy data fallback)
export const useDeleteGolfer = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      try {
        const response = await apiClient.deleteGolfer(id);

        if (!response.success) {
          console.warn('XANO delete failed, simulating success');
          return { id }; // Simulate success
        }

        return { id };
      } catch (error: any) {
        console.warn('XANO connection failed during delete, simulating success');
        return { id }; // Simulate success
      }
    },
    onSuccess: (_, id) => {
      // Remove the golfer from cache and invalidate the list
      queryClient.removeQueries({ queryKey: ['golfer', id] });
      queryClient.invalidateQueries({ queryKey: ['golfers'] });
    },
    onError: (error) => {
      console.error('Failed to delete golfer:', error);
    },
  });
};