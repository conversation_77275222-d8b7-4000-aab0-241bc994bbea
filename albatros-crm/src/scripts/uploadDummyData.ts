/**
 * <PERSON><PERSON><PERSON> to upload dummy golfer data to XANO database
 * Run this to populate your XANO DB with sample data
 */

import { GolfCourseAPIClient } from '../api/GolfCourseAPIClient';

// Dummy golfer data to upload to XANO
const dummyGolfersF<PERSON><PERSON><PERSON> = [
  {
    first_name: '<PERSON>',
    last_name: '<PERSON>',
    email: '<EMAIL>',
    phone_number: '+****************',
    address: '123 Golf Lane, Scottsdale, AZ',
    handicap: 8,
    upcoming_tee_time: '2024-01-22T10:00:00Z',
    past_tee_times: [
      { date: '2024-01-15T09:30:00Z', score: 92 },
      { date: '2024-01-08T14:00:00Z', score: 89 },
      { date: '2024-01-01T11:15:00Z', score: 95 }
    ]
  },
  {
    first_name: '<PERSON>',
    last_name: '<PERSON>',
    email: '<EMAIL>',
    phone_number: '+****************',
    address: '456 Fairway Dr, Phoenix, AZ',
    handicap: 12,
    upcoming_tee_time: '2024-01-25T14:30:00Z',
    past_tee_times: [
      { date: '2024-01-10T10:00:00Z', score: 87 },
      { date: '2024-01-03T15:30:00Z', score: 91 }
    ]
  },
  {
    first_name: 'Mike',
    last_name: '<PERSON>',
    email: '<EMAIL>',
    phone_number: '+****************',
    address: '789 <PERSON> St, Tempe, AZ',
    handicap: 18,
    upcoming_tee_time: null,
    past_tee_times: [
      { date: '2024-01-08T16:00:00Z', score: 95 },
      { date: '2023-12-28T13:00:00Z', score: 102 }
    ]
  },
  {
    first_name: 'Emily',
    last_name: 'Chen',
    email: '<EMAIL>',
    phone_number: '+****************',
    address: '321 Birdie Blvd, Mesa, AZ',
    handicap: 6,
    upcoming_tee_time: '2024-01-20T08:00:00Z',
    past_tee_times: [
      { date: '2024-01-12T09:00:00Z', score: 84 },
      { date: '2024-01-05T10:30:00Z', score: 88 },
      { date: '2023-12-29T14:00:00Z', score: 82 }
    ]
  },
  {
    first_name: 'David',
    last_name: 'Wilson',
    email: '<EMAIL>',
    phone_number: '+****************',
    address: '654 Eagle Way, Chandler, AZ',
    handicap: 10,
    upcoming_tee_time: '2024-01-28T11:00:00Z',
    past_tee_times: [
      { date: '2024-01-05T12:00:00Z', score: 89 },
      { date: '2023-12-30T09:30:00Z', score: 93 }
    ]
  },
  {
    first_name: 'Lisa',
    last_name: 'Thompson',
    email: '<EMAIL>',
    phone_number: '+****************',
    address: '987 Par Ave, Glendale, AZ',
    handicap: 22,
    upcoming_tee_time: null,
    past_tee_times: [
      { date: '2024-01-03T15:00:00Z', score: 98 },
      { date: '2023-12-27T10:00:00Z', score: 105 }
    ]
  }
];

async function uploadDummyData() {
  const apiClient = new GolfCourseAPIClient();
  
  console.log('🚀 Starting dummy data upload to XANO...');
  console.log(`📊 Uploading ${dummyGolfersForXano.length} golfers`);
  
  let successCount = 0;
  let errorCount = 0;
  
  for (let i = 0; i < dummyGolfersForXano.length; i++) {
    const golfer = dummyGolfersForXano[i];
    
    try {
      console.log(`⏳ Uploading ${golfer.first_name} ${golfer.last_name}...`);
      
      const response = await apiClient.createGolfer(golfer);
      
      if (response.success) {
        console.log(`✅ Successfully created ${golfer.first_name} ${golfer.last_name}`);
        successCount++;
      } else {
        console.error(`❌ Failed to create ${golfer.first_name} ${golfer.last_name}:`, response.error);
        errorCount++;
      }
    } catch (error) {
      console.error(`💥 Error uploading ${golfer.first_name} ${golfer.last_name}:`, error);
      errorCount++;
    }
    
    // Add a small delay between requests to avoid rate limiting
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  console.log('\n📈 Upload Summary:');
  console.log(`✅ Successful uploads: ${successCount}`);
  console.log(`❌ Failed uploads: ${errorCount}`);
  console.log(`📊 Total attempts: ${dummyGolfersForXano.length}`);
  
  if (successCount > 0) {
    console.log('\n🎉 Dummy data has been uploaded to XANO!');
    console.log('🔄 Refresh your roster page to see the real data from XANO.');
  }
  
  if (errorCount > 0) {
    console.log('\n⚠️  Some uploads failed. Check the errors above and your XANO configuration.');
  }
}

// Export for use in other files
export { uploadDummyData, dummyGolfersForXano };

// Run the upload if this file is executed directly
if (require.main === module) {
  uploadDummyData().catch(console.error);
}
