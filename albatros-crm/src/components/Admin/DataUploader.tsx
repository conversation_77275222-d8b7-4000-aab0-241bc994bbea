import React, { useState } from 'react';
import {
  <PERSON>,
  Button,
  Typography,
  Alert,
  CircularProgress,
  Paper,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
} from '@mui/material';
import {
  CloudUpload as UploadIcon,
  CheckCircle as SuccessIcon,
  Error as <PERSON>rrorIcon,
  Info as InfoIcon,
} from '@mui/icons-material';
import { GolfCourseAPIClient } from '../../api/GolfCourseAPIClient';

interface UploadResult {
  name: string;
  success: boolean;
  error?: string;
}

const dummyGolfersFor<PERSON><PERSON> = [
  {
    first_name: '<PERSON>',
    last_name: '<PERSON>',
    email: '<EMAIL>',
    phone_number: '+****************',
    address: '123 Golf Lane, Scottsdale, AZ',
    handicap: 8,
    upcoming_tee_time: '2024-01-22T10:00:00Z',
    past_tee_times: [
      { date: '2024-01-15T09:30:00Z', score: 92 },
      { date: '2024-01-08T14:00:00Z', score: 89 },
      { date: '2024-01-01T11:15:00Z', score: 95 }
    ]
  },
  {
    first_name: '<PERSON>',
    last_name: '<PERSON>',
    email: '<EMAIL>',
    phone_number: '+****************',
    address: '456 Fairway Dr, Phoenix, AZ',
    handicap: 12,
    upcoming_tee_time: '2024-01-25T14:30:00Z',
    past_tee_times: [
      { date: '2024-01-10T10:00:00Z', score: 87 },
      { date: '2024-01-03T15:30:00Z', score: 91 }
    ]
  },
  {
    first_name: 'Mike',
    last_name: 'Rodriguez',
    email: '<EMAIL>',
    phone_number: '+****************',
    address: '789 Green St, Tempe, AZ',
    handicap: 18,
    upcoming_tee_time: null,
    past_tee_times: [
      { date: '2024-01-08T16:00:00Z', score: 95 },
      { date: '2023-12-28T13:00:00Z', score: 102 }
    ]
  },
  {
    first_name: 'Emily',
    last_name: 'Chen',
    email: '<EMAIL>',
    phone_number: '+****************',
    address: '321 Birdie Blvd, Mesa, AZ',
    handicap: 6,
    upcoming_tee_time: '2024-01-20T08:00:00Z',
    past_tee_times: [
      { date: '2024-01-12T09:00:00Z', score: 84 },
      { date: '2024-01-05T10:30:00Z', score: 88 },
      { date: '2023-12-29T14:00:00Z', score: 82 }
    ]
  },
  {
    first_name: 'David',
    last_name: 'Wilson',
    email: '<EMAIL>',
    phone_number: '+****************',
    address: '654 Eagle Way, Chandler, AZ',
    handicap: 10,
    upcoming_tee_time: '2024-01-28T11:00:00Z',
    past_tee_times: [
      { date: '2024-01-05T12:00:00Z', score: 89 },
      { date: '2023-12-30T09:30:00Z', score: 93 }
    ]
  },
  {
    first_name: 'Lisa',
    last_name: 'Thompson',
    email: '<EMAIL>',
    phone_number: '+****************',
    address: '987 Par Ave, Glendale, AZ',
    handicap: 22,
    upcoming_tee_time: null,
    past_tee_times: [
      { date: '2024-01-03T15:00:00Z', score: 98 },
      { date: '2023-12-27T10:00:00Z', score: 105 }
    ]
  }
];

export const DataUploader: React.FC = () => {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadResults, setUploadResults] = useState<UploadResult[]>([]);
  const [uploadComplete, setUploadComplete] = useState(false);

  const uploadDummyData = async () => {
    setIsUploading(true);
    setUploadResults([]);
    setUploadComplete(false);

    const apiClient = new GolfCourseAPIClient();
    const results: UploadResult[] = [];

    for (const golfer of dummyGolfersForXano) {
      try {
        const response = await apiClient.createGolfer(golfer);
        
        results.push({
          name: `${golfer.first_name} ${golfer.last_name}`,
          success: response.success,
          error: response.success ? undefined : response.error?.message
        });
        
        setUploadResults([...results]);
        
        // Add delay between requests
        await new Promise(resolve => setTimeout(resolve, 500));
      } catch (error: any) {
        results.push({
          name: `${golfer.first_name} ${golfer.last_name}`,
          success: false,
          error: error.message
        });
        setUploadResults([...results]);
      }
    }

    setIsUploading(false);
    setUploadComplete(true);
  };

  const successCount = uploadResults.filter(r => r.success).length;
  const errorCount = uploadResults.filter(r => !r.success).length;

  return (
    <Paper sx={{ p: 3, maxWidth: 600, mx: 'auto' }}>
      <Typography variant="h5" gutterBottom>
        🏌️ Upload Dummy Golfer Data to XANO
      </Typography>
      
      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        This will upload 6 sample golfers to your XANO database. Make sure your XANO golfer table is properly configured.
      </Typography>

      {!uploadComplete && (
        <Button
          variant="contained"
          startIcon={isUploading ? <CircularProgress size={20} /> : <UploadIcon />}
          onClick={uploadDummyData}
          disabled={isUploading}
          size="large"
          sx={{ mb: 3 }}
        >
          {isUploading ? 'Uploading...' : 'Upload Dummy Data to XANO'}
        </Button>
      )}

      {uploadResults.length > 0 && (
        <Box sx={{ mt: 3 }}>
          <Typography variant="h6" gutterBottom>
            Upload Progress:
          </Typography>
          
          <List dense>
            {uploadResults.map((result, index) => (
              <ListItem key={index}>
                <ListItemIcon>
                  {result.success ? (
                    <SuccessIcon color="success" />
                  ) : (
                    <ErrorIcon color="error" />
                  )}
                </ListItemIcon>
                <ListItemText
                  primary={result.name}
                  secondary={result.error || 'Successfully uploaded'}
                />
              </ListItem>
            ))}
          </List>
        </Box>
      )}

      {uploadComplete && (
        <Box sx={{ mt: 3 }}>
          <Alert 
            severity={errorCount === 0 ? "success" : "warning"} 
            sx={{ mb: 2 }}
          >
            Upload completed! {successCount} successful, {errorCount} failed.
          </Alert>
          
          {successCount > 0 && (
            <Alert severity="info" icon={<InfoIcon />}>
              🔄 Refresh your roster page to see the real data from XANO!
            </Alert>
          )}
          
          <Button
            variant="outlined"
            onClick={() => {
              setUploadResults([]);
              setUploadComplete(false);
            }}
            sx={{ mt: 2 }}
          >
            Upload Again
          </Button>
        </Box>
      )}
    </Paper>
  );
};

export default DataUploader;
