import { ReactNode } from 'react';
import { XanoGolfer } from '../../api/GolfCourseAPIClient';

// Frontend Golfer interface (for UI components)
export interface Golfer {
  id: string;
  name: string;
  phone: string;
  email: string;
  stars: number;
  isMember: boolean;
  lastPlayDate: string;
  upcomingPlayDate: string;
  avatar?: string;
  avatarColor?: string;
  initials?: string;
  address?: string;
  albatrossStarScore?: string;
  nps?: string;
  events?: string;
  foodDrink?: string;
  student?: string;
  firstName?: string;
  lastName?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  handicap?: number;
}

// XANO-compatible golfer interface (matches backend schema)
export interface XanoGolferData {
  id: number;
  created_at: string;
  first_name: string;
  last_name: string;
  email: string;
  phone_number?: string;
  address?: string;
  city?: string;
  state?: string;
  zip_code?: string;
  country?: string;
  handicap?: number;
  upcoming_tee_time?: string;
  past_tee_times?: string[];
  sms_notifications_enabled?: boolean;
  email_notifications_enabled?: boolean;
  in_app_notifications_enabled?: boolean;
  preferred_table_location?: string;
  waitlist_enabled?: boolean;
  preferred_play_day_times?: string[];
  preferred_course?: string;
}

// Utility functions to convert between XANO and frontend formats
export const convertXanoToFrontend = (xanoGolfer: XanoGolfer): Golfer => {
  const fullName = `${xanoGolfer.first_name} ${xanoGolfer.last_name}`.trim();
  const initials = `${xanoGolfer.first_name.charAt(0)}${xanoGolfer.last_name.charAt(0)}`.toUpperCase();

  return {
    id: xanoGolfer.id.toString(),
    name: fullName,
    firstName: xanoGolfer.first_name,
    lastName: xanoGolfer.last_name,
    phone: xanoGolfer.phone_number || '',
    email: xanoGolfer.email,
    address: xanoGolfer.address || '',
    city: xanoGolfer.city || '',
    state: xanoGolfer.state || '',
    zipCode: xanoGolfer.zip_code || '',
    handicap: xanoGolfer.handicap || 0,
    stars: calculateStarRating(xanoGolfer), // Custom logic for star rating
    isMember: checkMembershipStatus(xanoGolfer), // Custom logic for membership
    lastPlayDate: getLastPlayDate(xanoGolfer),
    upcomingPlayDate: xanoGolfer.upcoming_tee_time || 'NONE',
    initials,
    avatar: undefined, // Will be generated or fetched separately
    avatarColor: generateAvatarColor(fullName),
    albatrossStarScore: '0', // Default or calculated value
    nps: '0', // Default or calculated value
    events: '', // Default or fetched separately
    foodDrink: '', // Default or fetched separately
    student: 'No' // Default or calculated value
  };
};

export const convertFrontendToXano = (golfer: GolferFormData): Partial<XanoGolfer> => {
  return {
    first_name: golfer.firstName,
    last_name: golfer.lastName,
    email: golfer.email,
    phone_number: golfer.phone || undefined,
    address: golfer.address || undefined,
    city: golfer.city || undefined,
    state: golfer.state || undefined,
    zip_code: golfer.zipCode || undefined,
    handicap: golfer.handicap || undefined,
    sms_notifications_enabled: golfer.smsNotifications ?? true,
    email_notifications_enabled: golfer.emailNotifications ?? true,
    in_app_notifications_enabled: golfer.inAppNotifications ?? true,
    preferred_table_location: golfer.preferredTableLocation || undefined,
  };
};

// Helper functions
const calculateStarRating = (xanoGolfer: XanoGolfer): number => {
  // Custom logic to calculate star rating based on golfer data
  // This could be based on handicap, play frequency, etc.
  if (xanoGolfer.handicap !== undefined) {
    if (xanoGolfer.handicap <= 5) return 5;
    if (xanoGolfer.handicap <= 10) return 4;
    if (xanoGolfer.handicap <= 15) return 3;
    if (xanoGolfer.handicap <= 20) return 2;
    return 1;
  }
  return 3; // Default rating
};

const checkMembershipStatus = (xanoGolfer: XanoGolfer): boolean => {
  // Custom logic to determine membership status
  // This might be based on a separate membership table or field
  return false; // Default to non-member for now
};

const getLastPlayDate = (xanoGolfer: XanoGolfer): string => {
  // Extract last play date from past_tee_times or other source
  if (xanoGolfer.past_tee_times && xanoGolfer.past_tee_times.length > 0) {
    // Return the most recent date
    const dates = xanoGolfer.past_tee_times.map(date => new Date(date));
    const mostRecent = new Date(Math.max(...dates.map(d => d.getTime())));
    return mostRecent.toISOString().split('T')[0];
  }
  return new Date().toISOString().split('T')[0]; // Default to today
};

const generateAvatarColor = (name: string): string => {
  // Generate a consistent color based on name
  const colors = ['#1976d2', '#42a5f5', '#3B82F6', '#10B981', '#F59E0B', '#EF4444'];
  const hash = name.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
  return colors[hash % colors.length];
};

export interface GolferListItemProps {
  golfer: Golfer;
  onEdit: (golfer: Golfer) => void;
}

export interface GolferFormData {
  id?: string;
  firstName: string;
  lastName: string;
  phone: string;
  email: string;
  address: string;
  city?: string;
  state?: string;
  zipCode?: string;
  handicap?: number;
  stars: number;
  isMember: boolean;
  upcomingPlay: string;
  lastSeen: string;
  albatrossStarScore: string;
  nps: string;
  events: string;
  foodDrink: string;
  student: string;
  avatar?: string;
  avatarColor?: string;
  smsNotifications?: boolean;
  emailNotifications?: boolean;
  inAppNotifications?: boolean;
  preferredTableLocation?: string;
}

export interface GolferFormModalProps {
  open: boolean;
  onClose: () => void;
  golfer: GolferFormData | null;
  onSubmit: (data: GolferFormData) => void;
  mode?: 'add' | 'edit';
}

export interface GolferMetricsCardProps {
  icon: ReactNode;
  label: string;
  value: string | number;
  change?: number;
  bgColor?: string;
}

export interface SortOption {
  id: string;
  label: string;
  value: 'name' | 'stars' | 'lastPlayDate';
  direction: 'asc' | 'desc';
}

export interface SortMenuProps {
  anchorEl: HTMLElement | null;
  open: boolean;
  onClose: () => void;
  currentSort: SortOption | null;
  onSortChange: (option: SortOption) => void;
}

export type FilterValue = number | 'member' | 'non-member' | 'upcoming' | 'recent';

export interface Filters {
  stars: number[];
  membership: Array<'member' | 'non-member'>;
  playStatus: Array<'upcoming' | 'recent'>;
}

export interface FilterMenuProps {
  anchorEl: HTMLElement | null;
  open: boolean;
  onClose: () => void;
  filters: Filters;
  onFilterChange: (category: keyof Filters, value: Filters[keyof Filters][number]) => void;
}

export interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
} 