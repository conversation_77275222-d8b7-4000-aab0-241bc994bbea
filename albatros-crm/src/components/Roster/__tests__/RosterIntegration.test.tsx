import React from 'react';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Roster } from '../Roster';
import { GolfCourseAPIClient } from '../../../api/GolfCourseAPIClient';

// Mock the API client
jest.mock('../../../api/GolfCourseAPIClient');

const MockedGolfCourseAPIClient = GolfCourseAPIClient as jest.MockedClass<typeof GolfCourseAPIClient>;

// Mock data that matches XANO schema
const mockXanoGolfers = [
  {
    id: 1,
    created_at: '2024-01-01T00:00:00Z',
    first_name: '<PERSON>',
    last_name: '<PERSON><PERSON>',
    email: '<EMAIL>',
    phone_number: '+**********',
    address: '123 Main St',
    city: 'Anytown',
    state: 'CA',
    zip_code: '12345',
    handicap: 10,
    upcoming_tee_time: '2024-12-25T10:00:00Z',
    past_tee_times: ['2024-12-01T09:00:00Z'],
    sms_notifications_enabled: true,
    email_notifications_enabled: true,
    in_app_notifications_enabled: true,
  },
  {
    id: 2,
    created_at: '2024-01-02T00:00:00Z',
    first_name: 'Jane',
    last_name: 'Smith',
    email: '<EMAIL>',
    phone_number: '+1987654321',
    address: '456 Oak Ave',
    city: 'Somewhere',
    state: 'NY',
    zip_code: '54321',
    handicap: 15,
    upcoming_tee_time: null,
    past_tee_times: [],
    sms_notifications_enabled: false,
    email_notifications_enabled: true,
    in_app_notifications_enabled: true,
  }
];

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
    mutations: {
      retry: false,
    },
  },
});

const renderWithQueryClient = (component: React.ReactElement) => {
  const queryClient = createTestQueryClient();
  return render(
    <QueryClientProvider client={queryClient}>
      {component}
    </QueryClientProvider>
  );
};

describe('Roster XANO Integration', () => {
  let mockApiClient: jest.Mocked<GolfCourseAPIClient>;

  beforeEach(() => {
    mockApiClient = {
      getGolfers: jest.fn(),
      createGolfer: jest.fn(),
      updateGolfer: jest.fn(),
      deleteGolfer: jest.fn(),
    } as any;

    MockedGolfCourseAPIClient.mockImplementation(() => mockApiClient);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  test('should load golfers from XANO API on mount', async () => {
    mockApiClient.getGolfers.mockResolvedValue({
      success: true,
      data: mockXanoGolfers,
    });

    renderWithQueryClient(<Roster />);

    // Should show loading state initially
    expect(screen.getByText('Loading golfers...')).toBeInTheDocument();

    // Wait for golfers to load
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('Jane Smith')).toBeInTheDocument();
    });

    // Verify API was called
    expect(mockApiClient.getGolfers).toHaveBeenCalledTimes(1);
  });

  test('should show error state when API fails', async () => {
    mockApiClient.getGolfers.mockResolvedValue({
      success: false,
      error: { code: 'API_ERROR', message: 'Failed to fetch golfers' },
    });

    renderWithQueryClient(<Roster />);

    await waitFor(() => {
      expect(screen.getByText(/Failed to load golfers/)).toBeInTheDocument();
    });
  });

  test('should display golfer metrics correctly', async () => {
    mockApiClient.getGolfers.mockResolvedValue({
      success: true,
      data: mockXanoGolfers,
    });

    renderWithQueryClient(<Roster />);

    await waitFor(() => {
      expect(screen.getByText('Total Golfers')).toBeInTheDocument();
      expect(screen.getByText('2')).toBeInTheDocument(); // Total count
    });
  });

  test('should open add golfer modal when add button is clicked', async () => {
    mockApiClient.getGolfers.mockResolvedValue({
      success: true,
      data: mockXanoGolfers,
    });

    renderWithQueryClient(<Roster />);

    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });

    // Click add button
    const addButton = screen.getByRole('button', { name: /add/i });
    fireEvent.click(addButton);

    // Should open the modal (assuming it has a specific text or role)
    await waitFor(() => {
      // This would depend on the actual modal implementation
      // For now, we'll just verify the button was clickable
      expect(addButton).toBeInTheDocument();
    });
  });

  test('should handle golfer creation successfully', async () => {
    const newGolfer = {
      id: 3,
      created_at: '2024-01-03T00:00:00Z',
      first_name: 'Bob',
      last_name: 'Johnson',
      email: '<EMAIL>',
      phone_number: '+1555666777',
      address: '789 Pine St',
      handicap: 8,
    };

    mockApiClient.getGolfers.mockResolvedValue({
      success: true,
      data: mockXanoGolfers,
    });

    mockApiClient.createGolfer.mockResolvedValue({
      success: true,
      data: newGolfer,
    });

    renderWithQueryClient(<Roster />);

    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });

    // The actual form submission would be tested in the form component tests
    // Here we just verify the API client setup
    expect(mockApiClient.getGolfers).toHaveBeenCalled();
  });
});
