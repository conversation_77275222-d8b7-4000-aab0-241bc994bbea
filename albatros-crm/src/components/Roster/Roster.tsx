import React, { useState } from 'react';
import {
  Box,
  Tabs,
  Tab,
  IconButton,
  Menu,
  MenuItem,
  Typography,
  Chip,
  CircularProgress,
  Alert,
  Snackbar,
  Paper,
  Button
} from '@mui/material';
import {
  Add as AddIcon,
  Sort as SortIcon,
  FilterList as FilterIcon
} from '@mui/icons-material';
import { Golfer, GolferFormData, SortOption, Filters, TabPanelProps } from './types';
import GolferListItem from './GolferListItem';
import GolferFormModal from './GolferFormModal';
import GolferMetricsCard from './GolferMetricsCard';
import { useGolfers, useCreateGolfer, useUpdateGolfer, useDeleteGolfer } from '../../api/hooks/useGolfers';

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index }) => (
  <div hidden={value !== index} role="tabpanel">
    {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
  </div>
);

export const Roster: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [selectedGolfer, setSelectedGolfer] = useState<Golfer | null>(null);
  const [formModalOpen, setFormModalOpen] = useState(false);

  const [sortAnchorEl, setSortAnchorEl] = useState<null | HTMLElement>(null);
  const [filterAnchorEl, setFilterAnchorEl] = useState<null | HTMLElement>(null);
  const [currentSort, setCurrentSort] = useState<SortOption | null>(null);
  const [filters, setFilters] = useState<Filters>({
    stars: [],
    membership: [],
    playStatus: []
  });
  const [selectedGolfers, setSelectedGolfers] = useState<Set<string>>(new Set());
  const [snackbar, setSnackbar] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error' | 'info' | 'warning';
  }>({
    open: false,
    message: '',
    severity: 'success'
  });

  // Clear any cached mock data on component mount
  React.useEffect(() => {
    // Clear localStorage mock data to ensure we only show XANO data
    localStorage.removeItem('roster_golfers_data');
  }, []);

  // XANO API hooks
  const { data: golfers = [], isLoading, error, refetch } = useGolfers();
  const createGolferMutation = useCreateGolfer();
  const updateGolferMutation = useUpdateGolfer();
  const deleteGolferMutation = useDeleteGolfer();

  // Check if this is a XANO setup issue
  const isXanoSetupError = error?.message?.includes('Resource not found') ||
                          error?.message?.includes('404') ||
                          error?.message?.includes('ENDPOINT_NOT_FOUND');

  const showSnackbar = (message: string, severity: 'success' | 'error' | 'info' | 'warning' = 'success') => {
    setSnackbar({ open: true, message, severity });
  };

  const handleSnackbarClose = () => {
    setSnackbar(prev => ({ ...prev, open: false }));
  };

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const formatDateToYYYYMMDD = (dateStr: string): string => {
    if (!dateStr) return '';
    try {
      // Try to parse the date in various formats
      const date = new Date(dateStr);
      if (isNaN(date.getTime())) {
        // If the date is in MM-DD-YYYY format, try to parse it
        const parts = dateStr.split('-');
        if (parts.length === 3) {
          const [month, day, year] = parts;
          const newDate = new Date(`${year}-${month}-${day}`);
          if (!isNaN(newDate.getTime())) {
            return newDate.toISOString().split('T')[0];
          }
        }
        return '';
      }
      return date.toISOString().split('T')[0];
    } catch (error) {
      console.error('Error formatting date:', error);
      return '';
    }
  };

  const handleEditGolfer = (golfer: Golfer) => {
    // Create a clean copy of the golfer data
    const cleanGolfer = {
      ...golfer,
      name: golfer.name || '',
      phone: golfer.phone || '',
      email: golfer.email || '',
      address: golfer.address || '',
      albatrossStarScore: golfer.albatrossStarScore || '',
      nps: golfer.nps || '',
      events: golfer.events || '',
      foodDrink: golfer.foodDrink || '',
      student: golfer.student || '',
      lastPlayDate: formatDateToYYYYMMDD(golfer.lastPlayDate) || '',
      upcomingPlayDate: formatDateToYYYYMMDD(golfer.upcomingPlayDate) || '',
    };

    // Data is now handled by the form modal directly

    setSelectedGolfer(cleanGolfer);
    setFormModalOpen(true);
  };

  const handleAddGolfer = () => {
    setSelectedGolfer(null);
    setFormModalOpen(true);
  };

  const handleFormSubmit = async (data: GolferFormData) => {
    try {
      console.log('Form Data Received:', data);
      console.log('Selected Golfer:', selectedGolfer);

      if (selectedGolfer) {
        // Update existing golfer
        await updateGolferMutation.mutateAsync({
          id: selectedGolfer.id,
          data: data
        });
        showSnackbar('Golfer updated successfully!', 'success');
      } else {
        // Create new golfer
        await createGolferMutation.mutateAsync(data);
        showSnackbar('Golfer created successfully!', 'success');
      }

      // Close the modal and reset selection
      setFormModalOpen(false);
      setSelectedGolfer(null);
    } catch (error) {
      console.error('Error saving golfer data:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to save golfer';
      showSnackbar(errorMessage, 'error');
    }
  };

  const handleFilterClick = (event: React.MouseEvent<HTMLElement>) => {
    setFilterAnchorEl(event.currentTarget);
  };

  const handleFilterClose = () => {
    setFilterAnchorEl(null);
  };

  const handleFilterChange = (category: keyof Filters, value: Filters[keyof Filters][number]) => {
    setFilters(prev => {
      const currentFilters = prev[category] as Array<Filters[typeof category][number]>;
      const hasValue = currentFilters.includes(value);
      return {
        ...prev,
        [category]: hasValue 
          ? currentFilters.filter(v => v !== value)
          : [...currentFilters, value]
      };
    });
  };

  const handleSelectGolfer = (golferId: string) => {
    setSelectedGolfers(prev => {
      const newSelected = new Set(prev);
      if (newSelected.has(golferId)) {
        newSelected.delete(golferId);
      } else {
        newSelected.add(golferId);
      }
      return newSelected;
    });
  };

  const filteredGolfers = golfers.filter(golfer => {
    if (filters.stars.length && !filters.stars.includes(golfer.stars)) return false;
    if (filters.membership.length) {
      if (filters.membership.includes('member') && !golfer.isMember) return false;
      if (filters.membership.includes('non-member') && golfer.isMember) return false;
    }
    if (filters.playStatus.length) {
      const hasUpcoming = new Date(golfer.upcomingPlayDate) > new Date();
      const hasRecent = new Date(golfer.lastPlayDate) > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      if (filters.playStatus.includes('upcoming') && !hasUpcoming) return false;
      if (filters.playStatus.includes('recent') && !hasRecent) return false;
    }
    return true;
  });

  // Show loading state
  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '400px' }}>
        <CircularProgress />
        <Typography variant="h6" sx={{ ml: 2 }}>Loading golfers...</Typography>
      </Box>
    );
  }

  // Show error state
  if (error) {
    const isSetupError = error.message?.includes('ENDPOINT_NOT_FOUND') ||
                        error.message?.includes('Resource not found') ||
                        error.message?.includes('404');

    if (isSetupError) {
      return (
        <Box sx={{ p: 4, textAlign: 'center', maxWidth: 800, mx: 'auto' }}>
          <Typography variant="h4" color="warning.main" gutterBottom>
            🔧 XANO Database Setup Required
          </Typography>

          <Typography variant="h6" sx={{ mb: 3 }}>
            Your XANO database needs a "golfer" table and API endpoints to be created.
          </Typography>

          <Paper sx={{ p: 3, textAlign: 'left', bgcolor: 'grey.50', mb: 3 }}>
            <Typography variant="h6" gutterBottom color="primary">
              📋 Step-by-Step XANO Setup:
            </Typography>

            <Typography variant="body1" component="div" sx={{ mb: 2 }}>
              <strong>1. Create the Golfer Table:</strong>
              <ul style={{ marginTop: 8, marginLeft: 20 }}>
                <li>Go to your XANO workspace: <strong>https://app.xano.com</strong></li>
                <li>Navigate to <strong>Database → Tables</strong></li>
                <li>Click <strong>"Add Table"</strong></li>
                <li>Name it: <strong>"golfer"</strong> (lowercase)</li>
              </ul>
            </Typography>

            <Typography variant="body1" component="div" sx={{ mb: 2 }}>
              <strong>2. Add These Fields to the Golfer Table:</strong>
              <ul style={{ marginTop: 8, marginLeft: 20 }}>
                <li><code>id</code> - Auto-increment (Primary Key) ✅ Auto-created</li>
                <li><code>first_name</code> - Text</li>
                <li><code>last_name</code> - Text</li>
                <li><code>email</code> - Text</li>
                <li><code>phone_number</code> - Text</li>
                <li><code>address</code> - Text</li>
                <li><code>handicap</code> - Integer</li>
                <li><code>upcoming_tee_time</code> - DateTime</li>
                <li><code>past_tee_times</code> - JSON</li>
              </ul>
            </Typography>

            <Typography variant="body1" component="div" sx={{ mb: 2 }}>
              <strong>3. Create API Endpoints:</strong>
              <ul style={{ marginTop: 8, marginLeft: 20 }}>
                <li>Go to <strong>API → Functions</strong></li>
                <li>Create these endpoints for the golfer table:</li>
                <li style={{ marginLeft: 20 }}>• <code>GET /golfer</code> - Get all golfers</li>
                <li style={{ marginLeft: 20 }}>• <code>POST /golfer</code> - Create golfer</li>
                <li style={{ marginLeft: 20 }}>• <code>GET /golfer/{`{id}`}</code> - Get single golfer</li>
                <li style={{ marginLeft: 20 }}>• <code>PATCH /golfer/{`{id}`}</code> - Update golfer</li>
                <li style={{ marginLeft: 20 }}>• <code>DELETE /golfer/{`{id}`}</code> - Delete golfer</li>
              </ul>
            </Typography>

            <Typography variant="body1" component="div">
              <strong>4. Publish Your API:</strong>
              <ul style={{ marginTop: 8, marginLeft: 20 }}>
                <li>Make sure to <strong>publish</strong> your API changes</li>
                <li>Test the endpoints in XANO's API explorer</li>
              </ul>
            </Typography>
          </Paper>

          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            <strong>Current XANO URL:</strong> <code>https://x8ki-letl-twmt.n7.xano.io/api:uP98JIiJ</code>
          </Typography>

          <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
            <Button
              variant="contained"
              onClick={() => {
                console.log('🔄 Manual connection test triggered');
                refetch();
              }}
              size="large"
            >
              🔄 Test Connection Again
            </Button>
            <Button
              variant="outlined"
              onClick={async () => {
                console.log('🧪 Running detailed XANO test...');
                try {
                  const response = await fetch('https://x8ki-letl-twmt.n7.xano.io/api:uP98JIiJ/golfer');
                  console.log('✅ Direct fetch response status:', response.status);
                  console.log('✅ Direct fetch response headers:', response.headers);
                  const data = await response.json();
                  console.log('✅ Direct fetch data:', data);
                } catch (error) {
                  console.error('❌ Direct fetch error:', error);
                }
              }}
              size="large"
            >
              🧪 Test Direct Connection
            </Button>
            <Button
              variant="outlined"
              href="https://app.xano.com"
              target="_blank"
              size="large"
            >
              🚀 Open XANO Dashboard
            </Button>
            <Button
              variant="outlined"
              href="https://docs.xano.com/getting-started"
              target="_blank"
              size="large"
            >
              📚 XANO Documentation
            </Button>
          </Box>
        </Box>
      );
    }

    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error" sx={{ mb: 2 }}>
          Failed to load golfers: {error.message}
        </Alert>
        <Box sx={{ display: 'flex', justifyContent: 'center' }}>
          <IconButton onClick={() => refetch()} color="primary">
            <Typography>Retry</Typography>
          </IconButton>
        </Box>
      </Box>
    );
  }

  return (
    <Box>
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs value={tabValue} onChange={handleTabChange}>
          <Tab label="All Golfers" />
          <Tab label="Members" />
          <Tab label="Non-Members" />
        </Tabs>
      </Box>

      <TabPanel value={tabValue} index={0}>
        <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6">All Golfers</Typography>
          <Box>
            <IconButton onClick={handleFilterClick}>
              <FilterIcon />
            </IconButton>
            <IconButton onClick={(e) => setSortAnchorEl(e.currentTarget)}>
              <SortIcon />
            </IconButton>
            <IconButton onClick={handleAddGolfer}>
              <AddIcon />
            </IconButton>
          </Box>
        </Box>

        {/* Active Filters Display */}
        <Box sx={{ mb: 2, display: 'flex', flexWrap: 'wrap', gap: 1, alignItems: 'center' }}>
          {filters.stars.length > 0 && (
            <Chip
              label={`${filters.stars.join(', ')} Stars`}
              onDelete={() => filters.stars.forEach(star => handleFilterChange('stars', star))}
              color="primary"
              variant="outlined"
              size="small"
            />
          )}
          {filters.membership.length > 0 && (
            <Chip
              label={filters.membership.join(', ')}
              onDelete={() => filters.membership.forEach(member => handleFilterChange('membership', member))}
              color="primary"
              variant="outlined"
              size="small"
            />
          )}
          {filters.playStatus.length > 0 && (
            <Chip
              label={filters.playStatus.join(', ')}
              onDelete={() => filters.playStatus.forEach(status => handleFilterChange('playStatus', status))}
              color="primary"
              variant="outlined"
              size="small"
            />
          )}
          {currentSort && (
            <Chip
              label={`Sorted by: ${currentSort}`}
              onDelete={() => setCurrentSort(null)}
              color="secondary"
              variant="outlined"
              size="small"
            />
          )}
          {filters.stars.length === 0 && filters.membership.length === 0 && 
           filters.playStatus.length === 0 && !currentSort && (
            <Typography variant="body2" color="text.secondary">
              No filters applied
            </Typography>
          )}
        </Box>

        <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: 3, mb: 3 }}>
          <GolferMetricsCard
            title="Total Golfers"
            value={golfers.length}
            change={5}
            period="vs last month"
          />
          <GolferMetricsCard
            title="Members"
            value={golfers.filter(g => g.isMember).length}
            change={3}
            period="vs last month"
          />
          <GolferMetricsCard
            title="Non-Members"
            value={golfers.filter(g => !g.isMember).length}
            change={2}
            period="vs last month"
          />
        </Box>

        <Box sx={{ bgcolor: 'background.paper', borderRadius: 1, boxShadow: 1 }}>
          {filteredGolfers.map(golfer => (
            <GolferListItem
              key={golfer.id}
              golfer={golfer}
              onEdit={() => handleEditGolfer(golfer)}
              selected={selectedGolfers.has(golfer.id.toString())}
              onSelect={() => handleSelectGolfer(golfer.id.toString())}
            />
          ))}
        </Box>
      </TabPanel>

      <TabPanel value={tabValue} index={1}>
        <Typography variant="h6">Members</Typography>
      </TabPanel>

      <TabPanel value={tabValue} index={2}>
        <Typography variant="h6">Non-Members</Typography>
      </TabPanel>

      <Menu
        anchorEl={filterAnchorEl}
        open={Boolean(filterAnchorEl)}
        onClose={handleFilterClose}
      >
        <MenuItem onClick={() => handleFilterChange('stars', 5)}>5 Stars</MenuItem>
        <MenuItem onClick={() => handleFilterChange('stars', 4)}>4 Stars</MenuItem>
        <MenuItem onClick={() => handleFilterChange('stars', 3)}>3 Stars</MenuItem>
        <MenuItem onClick={() => handleFilterChange('membership', 'member')}>Members</MenuItem>
        <MenuItem onClick={() => handleFilterChange('membership', 'non-member')}>Non-Members</MenuItem>
        <MenuItem onClick={() => handleFilterChange('playStatus', 'upcoming')}>Upcoming</MenuItem>
        <MenuItem onClick={() => handleFilterChange('playStatus', 'recent')}>Recent</MenuItem>
      </Menu>

      <GolferFormModal
        open={formModalOpen}
        onClose={() => {
          setFormModalOpen(false);
          setSelectedGolfer(null);
        }}
        onSubmit={handleFormSubmit}
        golfer={selectedGolfer ? {
          id: selectedGolfer.id,
          firstName: selectedGolfer.name.split(' ')[0] || '',
          lastName: selectedGolfer.name.split(' ').slice(1).join(' ') || '',
          phone: selectedGolfer.phone || '',
          email: selectedGolfer.email || '',
          address: selectedGolfer.address || '',
          stars: selectedGolfer.stars,
          isMember: selectedGolfer.isMember,
          upcomingPlay: selectedGolfer.upcomingPlayDate || '',
          lastSeen: selectedGolfer.lastPlayDate || '',
          albatrossStarScore: selectedGolfer.albatrossStarScore || '',
          nps: selectedGolfer.nps || '',
          events: selectedGolfer.events || '',
          foodDrink: selectedGolfer.foodDrink || '',
          student: selectedGolfer.student || '',
          avatar: selectedGolfer.avatar,
          avatarColor: selectedGolfer.avatarColor
        } : null}
      />

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={handleSnackbarClose} severity={snackbar.severity} sx={{ width: '100%' }}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};