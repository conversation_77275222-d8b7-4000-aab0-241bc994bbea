#!/usr/bin/env node

/**
 * Simple Node.js script to upload dummy golfer data to XANO
 * Run with: node upload-dummy-data.js
 */

const axios = require('axios');

const XANO_BASE_URL = 'https://x8ki-letl-twmt.n7.xano.io/api:uP98JIiJ';

const dummyGolfers = [
  {
    first_name: '<PERSON>',
    last_name: '<PERSON>',
    email: '<EMAIL>',
    phone_number: '+****************',
    address: '123 Golf Lane, Scottsdale, AZ',
    handicap: 8,
    upcoming_tee_time: '2024-01-22T10:00:00Z',
    past_tee_times: [
      { date: '2024-01-15T09:30:00Z', score: 92 },
      { date: '2024-01-08T14:00:00Z', score: 89 },
      { date: '2024-01-01T11:15:00Z', score: 95 }
    ]
  },
  {
    first_name: '<PERSON>',
    last_name: '<PERSON>',
    email: '<EMAIL>',
    phone_number: '+****************',
    address: '456 Fairway Dr, Phoenix, AZ',
    handicap: 12,
    upcoming_tee_time: '2024-01-25T14:30:00Z',
    past_tee_times: [
      { date: '2024-01-10T10:00:00Z', score: 87 },
      { date: '2024-01-03T15:30:00Z', score: 91 }
    ]
  },
  {
    first_name: 'Mike',
    last_name: 'Rodriguez',
    email: '<EMAIL>',
    phone_number: '+****************',
    address: '789 Green St, Tempe, AZ',
    handicap: 18,
    upcoming_tee_time: null,
    past_tee_times: [
      { date: '2024-01-08T16:00:00Z', score: 95 },
      { date: '2023-12-28T13:00:00Z', score: 102 }
    ]
  },
  {
    first_name: 'Emily',
    last_name: 'Chen',
    email: '<EMAIL>',
    phone_number: '+****************',
    address: '321 Birdie Blvd, Mesa, AZ',
    handicap: 6,
    upcoming_tee_time: '2024-01-20T08:00:00Z',
    past_tee_times: [
      { date: '2024-01-12T09:00:00Z', score: 84 },
      { date: '2024-01-05T10:30:00Z', score: 88 },
      { date: '2023-12-29T14:00:00Z', score: 82 }
    ]
  },
  {
    first_name: 'David',
    last_name: 'Wilson',
    email: '<EMAIL>',
    phone_number: '+****************',
    address: '654 Eagle Way, Chandler, AZ',
    handicap: 10,
    upcoming_tee_time: '2024-01-28T11:00:00Z',
    past_tee_times: [
      { date: '2024-01-05T12:00:00Z', score: 89 },
      { date: '2023-12-30T09:30:00Z', score: 93 }
    ]
  },
  {
    first_name: 'Lisa',
    last_name: 'Thompson',
    email: '<EMAIL>',
    phone_number: '+****************',
    address: '987 Par Ave, Glendale, AZ',
    handicap: 22,
    upcoming_tee_time: null,
    past_tee_times: [
      { date: '2024-01-03T15:00:00Z', score: 98 },
      { date: '2023-12-27T10:00:00Z', score: 105 }
    ]
  }
];

async function uploadDummyData() {
  console.log('🚀 Starting dummy data upload to XANO...');
  console.log(`📊 Uploading ${dummyGolfers.length} golfers to ${XANO_BASE_URL}`);
  console.log('');

  let successCount = 0;
  let errorCount = 0;

  for (let i = 0; i < dummyGolfers.length; i++) {
    const golfer = dummyGolfers[i];
    
    try {
      console.log(`⏳ [${i + 1}/${dummyGolfers.length}] Uploading ${golfer.first_name} ${golfer.last_name}...`);
      
      const response = await axios.post(`${XANO_BASE_URL}/golfer`, golfer, {
        headers: {
          'Content-Type': 'application/json',
        },
        timeout: 10000, // 10 second timeout
      });
      
      if (response.status === 200 || response.status === 201) {
        console.log(`✅ Successfully created ${golfer.first_name} ${golfer.last_name} (ID: ${response.data.id})`);
        successCount++;
      } else {
        console.log(`❌ Unexpected response for ${golfer.first_name} ${golfer.last_name}: ${response.status}`);
        errorCount++;
      }
    } catch (error) {
      console.log(`💥 Error uploading ${golfer.first_name} ${golfer.last_name}:`);
      if (error.response) {
        console.log(`   Status: ${error.response.status}`);
        console.log(`   Message: ${error.response.data?.message || error.response.statusText}`);
      } else if (error.request) {
        console.log(`   Network error: ${error.message}`);
      } else {
        console.log(`   Error: ${error.message}`);
      }
      errorCount++;
    }
    
    // Add a small delay between requests to avoid rate limiting
    if (i < dummyGolfers.length - 1) {
      await new Promise(resolve => setTimeout(resolve, 500));
    }
  }
  
  console.log('');
  console.log('📈 Upload Summary:');
  console.log(`✅ Successful uploads: ${successCount}`);
  console.log(`❌ Failed uploads: ${errorCount}`);
  console.log(`📊 Total attempts: ${dummyGolfers.length}`);
  
  if (successCount > 0) {
    console.log('');
    console.log('🎉 Dummy data has been uploaded to XANO!');
    console.log('🔄 Refresh your roster page to see the real data from XANO.');
  }
  
  if (errorCount > 0) {
    console.log('');
    console.log('⚠️  Some uploads failed. Check the errors above and your XANO configuration.');
    console.log('💡 Make sure your XANO golfer table exists and the API endpoints are published.');
  }
}

// Run the upload
uploadDummyData().catch(error => {
  console.error('💥 Fatal error:', error.message);
  process.exit(1);
});
