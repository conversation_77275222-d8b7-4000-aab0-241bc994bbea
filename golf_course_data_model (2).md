# Golf Course Management System - Data Model

## Overview
This data model represents a comprehensive golf course management system that handles operations including course management, tee time bookings, memberships, pro shop sales, food & beverage orders, coaching services, financial tracking, and customer relationship management.

## Core Entities

### Golf Course Management

#### golf_course
Primary entity representing golf courses in the system.
- `id` - Primary key
- `created_at` - Timestamp
- `course_id` - Unique course identifier
- `name` - Course name
- `address` - Physical address
- `phone_number` - Contact phone
- `email_address` - Contact email
- `course_logo` - Logo image reference
- `has_gps` - GPS availability flag
- `measurement` - Measurement system used
- `golf_course_hole_id` - Reference to hole data
- `course_settings` - Configuration settings
- `seasonal_rate` - Seasonal pricing information
- `course_holidays` - Holiday schedule
- `course_hours` - Operating hours
- `pace_of_play_data_id` - Reference to pace tracking
- `golf_course_data_api` - External API integration
- `google_average_rating` - Google reviews average
- `google_total_reviews` - Total Google reviews count
- `crm_average_course_quality` - Internal course quality rating
- `crm_average_food_quality` - Internal food quality rating
- `crm_average_overall_satisfaction` - Overall satisfaction rating
- `crm_total_reviews` - Total internal reviews
- `time_tracking_settings` - Employee time tracking configuration

#### golf_course_hole
Detailed information about each hole on the golf course.
- `id` - Primary key
- `created_at` - Timestamp
- `golf_course_id` - Foreign key to golf_course
- `hole_number` - Hole sequence number
- `hole_par_men` - Par for men
- `hole_par_women` - Par for women
- `blue_tees_distance` - Distance from blue tees
- `gold_tees_distance` - Distance from gold tees
- `white_tees_distance` - Distance from white tees
- `green_tees_distance` - Distance from green tees
- `red_tees_distance` - Distance from red tees
- `coordinates` - GPS coordinates
- `golf_course_hole_data` - Additional hole metadata

#### golf_course_announcement
Course announcements and notices.
- `id` - Primary key
- `created_at` - Timestamp
- `golf_course_id` - Foreign key to golf_course
- `announcement_title` - Announcement headline
- `announcement_content` - Full announcement text
- `start_datetime` - When announcement becomes active
- `end_datetime` - When announcement expires
- `global_data_id` - Reference to global data
- `ai_agent_interaction_id` - AI system interaction reference

### User Management

#### user
System users including staff and administrators.
- `id` - Primary key
- `created_at` - Timestamp
- `golf_course_id` - Foreign key to golf_course
- `name` - Full name
- `email` - Login email
- `password` - Encrypted password
- `corp_permission` - Corporate level access
- `region_permission` - Regional access rights
- `state_permission` - State level access
- `metro_permission` - Metropolitan area access
- `course_permission` - Course level permissions
- `calendar_access` - Calendar module access
- `roster_access` - Roster management access
- `pro_shop_access` - Pro shop access
- `nineteenth_hole_access` - F&B access
- `tournaments_access` - Tournament management access
- `analytics_access` - Analytics module access
- `back_office_access` - Administrative access
- `settings_access` - System settings access
- `user_role` - Role designation
- `avatar` - Profile image
- `last_login` - Last login timestamp
- `status` - Account status

#### user_activity_log
Audit trail for user actions.
- `id` - Primary key
- `created_at` - Timestamp
- `user_id` - Foreign key to user
- `action_timestamp` - When action occurred
- `module` - System module affected
- `action` - Action performed
- `details` - Additional action details
- `golf_course_id` - Associated course
- `global_data_id` - Global data reference

### Customer Management

#### golfer
Primary customer entity representing golf players.
- `id` - Primary key
- `created_at` - Timestamp
- `global_id` - Global system identifier
- `crm_golfer_id` - CRM system reference
- `first_name` - First name
- `last_name` - Last name
- `email` - Email address
- `phone_number` - Contact phone
- `address` - Street address
- `city` - City
- `state` - State/Province
- `zip_code` - Postal code
- `country` - Country
- `upcoming_tee_time` - Future bookings
- `past_tee_times` - Historical tee times
- `past_food_orders` - F&B order history
- `backfill_presented` - Backfill offer presented flag
- `backfill_accepted` - Backfill acceptance status
- `waitlist_enabled` - Waitlist participation flag
- `waitlist_dates_times` - Preferred waitlist slots
- `preferred_play_day_times` - Playing preferences
- `preferred_course` - Preferred course
- `round_history` - Playing history
- `saved_payment_methods` - Stored payment info
- `sms_notifications_enabled` - SMS notification preference
- `email_notifications_enabled` - Email notification preference
- `in_app_notifications_enabled` - In-app notification preference
- `preferred_table_location` - Restaurant seating preference
- `ai_agent_interaction_id` - AI interaction reference
- `payment_info_verified` - Payment verification status
- `campaign_progress` - Marketing campaign engagement
- `golfer_shipping_address_id` - Shipping address reference
- `handicap` - Golf handicap

#### golfer_group
Groups of golfers for coordinated activities.
- `id` - Primary key
- `created_at` - Timestamp
- `group_name` - Group identifier
- `golfer_ids` - List of group members
- `golfer_preferences` - Group preferences
- `golf_course_id` - Associated course
- `send_group_email` - Group email notification flag
- `send_group_sms` - Group SMS notification flag
- `send_group_in_app` - Group in-app notification flag

#### golfer_shipping_address
Shipping addresses for golfer purchases.
- `id` - Primary key
- `created_at` - Timestamp
- `golfer_id` - Foreign key to golfer
- `street_address` - Street address
- `city` - City
- `state` - State/Province
- `zip_code` - Postal code
- `country` - Country

#### golfer_funds
Golfer account balance and wallet functionality.
- `id` - Primary key
- `created_at` - Timestamp
- `golfer_id` - Foreign key to golfer
- `funds_balance` - Current balance
- `last_updated_at` - Last balance update

#### crm_course_review
Customer reviews and feedback.
- `id` - Primary key
- `created_at` - Timestamp
- `golf_course_id` - Foreign key to golf_course
- `golfer_id` - Foreign key to golfer
- `review_date` - When review was submitted
- `review_text` - Review content
- `course_quality_rating` - Course quality score
- `food_quality_rating` - Food quality score
- `overall_satisfaction_rating` - Overall satisfaction score

### Membership Management

#### membership
Golf course memberships and discounts.
- `id` - Primary key
- `created_at` - Timestamp
- `golf_course_id` - Foreign key to golf_course
- `membership_type` - Type of membership
- `start_date` - Membership start date
- `expiry_date` - Membership expiration
- `discount` - Discount percentage/amount
- `golfer_id` - Foreign key to golfer
- `membership_accepted_from_crm` - CRM integration flag

### Tee Time Management

#### calendar_event
Schedulable events including tee times and tournaments.
- `id` - Primary key
- `created_at` - Timestamp
- `golf_course_id` - Foreign key to golf_course
- `title` - Event title
- `description` - Event description
- `start_datetime` - Event start time
- `end_datetime` - Event end time
- `location` - Event location
- `event_type` - Type of event
- `tee_time_interval` - Tee time spacing
- `availability_outlook` - Availability status
- `cost_per_round` - Cost per golfer
- `discount_or_promotion_value` - Applied discounts
- `max_golfers` - Maximum participants
- `is_bookable` - Booking availability flag
- `global_data_id` - Global data reference
- `ai_agent_interaction_id` - AI interaction reference
- `membership_discount_id` - Membership discount reference
- `send_email_reminder` - Email reminder flag
- `send_sms_reminder` - SMS reminder flag
- `send_in_app_reminder` - In-app reminder flag

#### tee_time
Individual tee time slots.
- `id` - Primary key
- `created_at` - Timestamp
- `tee_time` - Scheduled time
- `number_of_players` - Expected players
- `golfer_id` - Primary golfer
- `golf_course_id` - Foreign key to golf_course
- `tee_time_cost_per_golfer` - Cost per player
- `split_tee_time_cost_with_other_golfer` - Cost sharing flag
- `tee_time_booking_id` - Booking reference
- `special_rate` - Special pricing applied
- `notes` - Additional notes

#### tee_time_booking
Booking records for tee times.
- `id` - Primary key
- `created_at` - Timestamp
- `calendar_event_id` - Foreign key to calendar_event
- `golfer_id` - Foreign key to golfer
- `booking_status` - Current booking status
- `global_data_id` - Global data reference

#### course_waitlist_setting
Waitlist configuration for courses.
- `id` - Primary key
- `created_at` - Timestamp
- `golf_course_id` - Foreign key to golf_course
- `waitlist_enabled` - Waitlist availability
- `max_waitlist_size` - Maximum waitlist capacity
- `waitlist_join_message` - Message when joining waitlist
- `tee_time_available_message` - Availability notification message
- `confirmation_timeframe` - Response time requirement
- `ai_agent_interaction_id` - AI interaction reference

#### post_round_suggestion
Post-game booking suggestions.
- `id` - Primary key
- `created_at` - Timestamp
- `golfer_id` - Foreign key to golfer
- `round_end_time` - When round completed
- `suggested_tee_times` - Recommended future times
- `response` - Golfer response
- `response_timestamp` - When response received
- `accepted_booking_id` - Accepted booking reference
- `golf_course_id` - Associated course

### Pro Shop Management

#### pro_shop_item
Merchandise and equipment available for purchase.
- `id` - Primary key
- `created_at` - Timestamp
- `golf_course_id` - Foreign key to golf_course
- `pro_shop_category_name` - Item category
- `pro_shop_item_name` - Item name
- `pro_shop_item_sku` - Stock keeping unit
- `description` - Item description
- `price` - Selling price
- `cost` - Item cost
- `membership_discount_offered` - Membership discount availability
- `membership_discount_amount` - Discount amount
- `pro_shop_item_photo` - Item image
- `pro_shop_mobile_item_offered` - Mobile app availability
- `inventory_in_stock` - Current stock level
- `total_inventory_necessary` - Minimum stock level
- `ai_agent_interaction_id` - AI interaction reference
- `membership_discount_id` - Discount reference
- `is_rentable` - Rental availability flag
- `rental_price` - Rental cost

#### pro_shop_item_transaction
Pro shop purchase transactions.
- `id` - Primary key
- `created_at` - Timestamp
- `quantity` - Items purchased
- `pro_shop_item_id` - Foreign key to pro_shop_item
- `transaction_id` - Transaction reference
- `ai_agent_interaction_id` - AI interaction reference
- `transaction_datetime` - Purchase timestamp
- `golfer_id` - Foreign key to golfer

### E-Commerce Management

#### e_commerce_item
Online merchandise catalog.
- `id` - Primary key
- `created_at` - Timestamp
- `pro_shop_item_id` - Reference to physical inventory
- `e_commerce_item_name` - Online item name
- `e_commerce_description` - Online description
- `e_commerce_price` - Online price
- `e_commerce_sku` - Online SKU
- `external_product_id` - External system reference
- `e_commerce_item_image` - Product image
- `in_stock` - Stock availability flag
- `quantity_in_stock` - Available quantity
- `global_data_id` - Global data reference
- `ai_agent_interaction_id` - AI interaction reference

#### e_commerce_transaction
Online purchase transactions.
- `id` - Primary key
- `created_at` - Timestamp
- `transaction_datetime` - Purchase timestamp
- `golfer_id` - Foreign key to golfer
- `total_amount` - Total transaction value
- `payment_method` - Payment type used
- `transaction_status` - Current status
- `shipping_address` - Delivery address
- `global_data_id` - Global data reference
- `ai_agent_interaction_id` - AI interaction reference
- `shipping_address_id` - Shipping address reference

#### e_commerce_transaction_item
Individual items within e-commerce transactions.
- `id` - Primary key
- `created_at` - Timestamp
- `e_commerce_transaction_id` - Foreign key to e_commerce_transaction
- `e_commerce_item_id` - Foreign key to e_commerce_item
- `quantity` - Items ordered
- `price_per_item` - Unit price
- `global_data_id` - Global data reference
- `ai_agent_interaction_id` - AI interaction reference

### Food & Beverage Management

#### food_and_beverage_item
Restaurant menu items and pricing.
- `id` - Primary key
- `created_at` - Timestamp
- `golf_course_id` - Foreign key to golf_course
- `food_and_beverage_category` - Item category
- `food_item_name` - Item name
- `description` - Item description
- `price` - Menu price
- `food_customization` - Customization options
- `cost` - Item cost
- `membership_offered_discount` - Membership discount availability
- `membership_discount_amount` - Discount amount
- `mobile_app_offered_food_item` - Mobile ordering availability
- `membership_discount_id` - Discount reference

#### food_and_beverage_item_transaction
F&B order transactions.
- `id` - Primary key
- `created_at` - Timestamp
- `quantity` - Items ordered
- `food_and_beverage_item_id` - Foreign key to food_and_beverage_item
- `transaction_id` - Transaction reference
- `order_status` - Current order status
- `golfer_id` - Foreign key to golfer
- `claimed_by_employee_id` - Staff member handling order
- `delivery_time` - Expected delivery time
- `delivered_by_employee_id` - Delivery staff member
- `ai_agent_interaction_id` - AI interaction reference
- `in_play_mode_id` - On-course ordering reference

#### food_and_beverage_order
F&B order management.
- `id` - Primary key
- `created_at` - Timestamp
- `food_and_beverage_item_transaction_id` - Transaction reference
- `golfer_id` - Foreign key to golfer
- `employee_id` - Staff member reference
- `order_status` - Current status

#### restaurant_table
Restaurant seating management.
- `id` - Primary key
- `created_at` - Timestamp
- `table_number` - Table identifier
- `number_of_seats` - Seating capacity
- `table_location` - Table location in restaurant
- `golf_course_id` - Foreign key to golf_course
- `global_data_id` - Global data reference
- `ai_agent_interaction_id` - AI interaction reference

#### restaurant_table_booking
Restaurant reservations.
- `id` - Primary key
- `created_at` - Timestamp
- `table_id` - Foreign key to restaurant_table
- `golfer_id` - Foreign key to golfer
- `booking_datetime` - Reservation time
- `number_of_guests` - Party size
- `booking_status` - Reservation status
- `special_requests` - Special requirements
- `golf_course_id` - Associated course
- `global_data_id` - Global data reference
- `ai_agent_interaction_id` - AI interaction reference

### Coaching & Instruction

#### coach
Golf instructors and coaching staff.
- `id` - Primary key
- `created_at` - Timestamp
- `coach_name` - Coach name
- `background` - Professional background
- `credentials` - Certifications and qualifications
- `contact_number` - Contact phone
- `email_address` - Contact email
- `golf_course_id` - Associated course

#### student
Students receiving golf instruction.
- `id` - Primary key
- `created_at` - Timestamp
- `golfer_id` - Foreign key to golfer
- `first_name` - First name
- `last_name` - Last name
- `email` - Contact email
- `phone_number` - Contact phone
- `coach_id` - Foreign key to coach
- `golf_course_id` - Associated course

#### coaching_session
Individual coaching sessions.
- `id` - Primary key
- `created_at` - Timestamp
- `coach_id` - Foreign key to coach
- `session_datetime` - Session time
- `duration_minutes` - Session length
- `session_type` - Type of instruction
- `session_notes` - Session notes
- `golf_course_id` - Associated course
- `student_id` - Foreign key to student
- `golfer_id` - Foreign key to golfer

#### session_student
Link between sessions and students.
- `id` - Primary key
- `created_at` - Timestamp
- `coaching_session_id` - Foreign key to coaching_session
- `student_id` - Foreign key to student

#### coach_communication
Communication between coaches and students.
- `id` - Primary key
- `created_at` - Timestamp
- `coach_id` - Foreign key to coach
- `student_id` - Foreign key to student
- `communication_datetime` - Communication timestamp
- `communication_method` - Communication channel
- `communication_details` - Message content

#### coach_inquiry
Student inquiries about coaching.
- `id` - Primary key
- `created_at` - Timestamp
- `golfer_id` - Foreign key to golfer
- `student_id` - Foreign key to student
- `coach_id` - Foreign key to coach
- `inquiry_datetime` - Inquiry timestamp
- `inquiry_details` - Inquiry content
- `inquiry_status` - Current status

### Swing Analysis & Performance

#### swing_analysis_data
Detailed swing metrics and measurements.
- `id` - Primary key
- `created_at` - Timestamp
- `golfer_id` - Foreign key to golfer
- `club_head_speed` - Club head speed measurement
- `backswing_time` - Backswing duration
- `downswing_time` - Downswing duration
- `tempo_ratio` - Swing tempo ratio
- `face_angle` - Club face angle at impact
- `swing_path` - Swing path measurement
- `acceleration` - Club acceleration data
- `recorded_at` - Recording timestamp

#### swing_analysis
Swing analysis sessions.
- `id` - Primary key
- `created_at` - Timestamp
- `coaching_session_id` - Foreign key to coaching_session
- `analysis_datetime` - Analysis timestamp
- `swing_data` - Swing measurement data
- `swing_analytics_data_id` - Reference to detailed metrics

#### shot_tracking
Individual shot tracking during lessons.
- `id` - Primary key
- `created_at` - Timestamp
- `coaching_session_id` - Foreign key to coaching_session
- `hole_number` - Hole where shot taken
- `shot_distance` - Shot distance
- `shot_result` - Shot outcome
- `shot_type` - Type of shot
- `coordinates` - Shot location coordinates

#### note_taking
Coaching session notes.
- `id` - Primary key
- `created_at` - Timestamp
- `coaching_session_id` - Foreign key to coaching_session
- `note_content` - Note text
- `note_datetime` - Note timestamp

#### video_storage
Video storage for coaching sessions.
- `id` - Primary key
- `created_at` - Timestamp
- `coaching_session_id` - Foreign key to coaching_session
- `video_url` - Video file location
- `video_description` - Video description
- `coach_video_url` - Coach's video response

### In-Play Experience

#### in_play_mode
Real-time on-course experience and tracking.
- `id` - Primary key
- `created_at` - Timestamp
- `golfer_id` - Foreign key to golfer
- `current_hole` - Current hole being played
- `scorecard_data` - Current scorecard
- `gps_location` - Current GPS position
- `leaderboard_data` - Group leaderboard
- `game_mode` - Type of game being played
- `student_mode_data` - Student coaching data
- `golf_course_id` - Foreign key to golf_course
- `wager_amount` - Active wager amount
- `wager_description` - Wager details
- `global_data_id` - Global data reference
- `group_golfer_ids` - Playing partners
- `ai_agent_interaction_id` - AI interaction reference
- `food_and_beverage_order_id` - Active F&B orders
- `hole_number` - Current hole number
- `hole_gps_coordinates` - Hole GPS coordinates
- `hole_distance` - Distance to hole
- `hole_par` - Hole par
- `golfer_wager_id` - Wager reference
- `golf_course_hole_id` - Hole reference
- `swing_analytics_data_id` - Swing data reference
- `game_rules` - Active game rules

#### pace_of_play_data
Pace of play monitoring.
- `id` - Primary key
- `created_at` - Timestamp
- `golfer_id` - Foreign key to golfer
- `current_hole` - Current hole
- `observation_datetime` - Observation time
- `time_behind_pace` - Time behind expected pace
- `pace_alert_sent` - Alert notification flag

#### golfer_wager
Wagering between golfers.
- `id` - Primary key
- `created_at` - Timestamp
- `golfer_id_1` - First golfer
- `golfer_id_2` - Second golfer
- `wager_amount` - Wager amount
- `wager_datetime` - Wager timestamp
- `wager_description` - Wager details
- `payment_status` - Payment status
- `winning_golfer_id` - Winner
- `transaction_id` - Payment transaction
- `golfer_id_3` - Third golfer (optional)
- `golfer_id_4` - Fourth golfer (optional)
- `cash_app_username` - Payment app username
- `cash_app_transaction_id` - Payment app transaction

### Financial Management

#### transaction
Core transaction records.
- `id` - Primary key
- `created_at` - Timestamp
- `transaction_amount` - Transaction value
- `payment_method` - Payment type
- `golfer_id` - Foreign key to golfer
- `payment_info_verified` - Payment verification status
- `transaction_datetime` - Transaction timestamp
- `transaction_status` - Current status
- `authorization_code` - Payment authorization
- `e_commerce_transaction_id` - E-commerce reference
- `pro_shop_item_transaction_id` - Pro shop reference
- `food_and_beverage_item_transaction_id` - F&B reference
- `rewards_program_id` - Rewards program reference
- `square_transaction_id` - Square payment reference
- `square_location_id` - Square location reference
- `transaction_uuid` - Unique transaction identifier
- `sensitive_payment_data_id` - Sensitive data reference
- `revenue_type` - Type of revenue
- `revenue_source` - Revenue source

#### sensitive_payment_data
Encrypted payment information.
- `id` - Primary key
- `created_at` - Timestamp
- `transaction_id` - Foreign key to transaction
- `encrypted_card_number` - Encrypted card number
- `expiration_date` - Card expiration
- `encrypted_cvv` - Encrypted CVV

#### fund_transfer
Account funding and transfers.
- `id` - Primary key
- `created_at` - Timestamp
- `golfer_id` - Foreign key to golfer
- `transfer_type` - Type of transfer
- `transfer_amount` - Transfer amount
- `transfer_datetime` - Transfer timestamp
- `transfer_description` - Transfer description
- `golfer_wager_id` - Wager reference
- `golfer_funds_id` - Account reference
- `bank_name` - Bank name
- `routing_number` - Bank routing number
- `account_number` - Account number

### Accounting

#### accounting_account
Chart of accounts.
- `id` - Primary key
- `created_at` - Timestamp
- `account_name` - Account name
- `account_type` - Account type
- `account_number` - Account number
- `golf_course_id` - Foreign key to golf_course
- `beginning_balance` - Starting balance
- `ending_balance` - Current balance
- `is_reconciled` - Reconciliation status
- `reconciliation_date` - Last reconciliation date
- `ai_agent_interaction_id` - AI interaction reference

#### accounting_period
Accounting periods for reporting.
- `id` - Primary key
- `created_at` - Timestamp
- `period_start_date` - Period start
- `period_end_date` - Period end
- `period_name` - Period identifier
- `golf_course_id` - Foreign key to golf_course
- `ai_agent_interaction_id` - AI interaction reference

#### accounting_transaction
Individual accounting entries.
- `id` - Primary key
- `created_at` - Timestamp
- `transaction_date` - Transaction date
- `description` - Transaction description
- `amount` - Transaction amount
- `transaction_type` - Type of entry
- `account_id` - Foreign key to accounting_account
- `golf_course_id` - Foreign key to golf_course
- `golfer_id` - Foreign key to golfer
- `e_commerce_transaction_item_id` - E-commerce reference
- `food_and_beverage_item_transaction_id` - F&B reference
- `ai_agent_interaction_id` - AI interaction reference

### Inventory & Purchasing

#### supplier
Vendor and supplier information.
- `id` - Primary key
- `created_at` - Timestamp
- `supplier_name` - Supplier name
- `contact_email` - Contact email
- `contact_number` - Contact phone
- `supplier_address` - Supplier address
- `website` - Supplier website
- `ai_agent_interaction_id` - AI interaction reference

#### purchase_order
Purchase orders for inventory.
- `id` - Primary key
- `created_at` - Timestamp
- `order_date` - Order placement date
- `supplier_id` - Foreign key to supplier
- `total_amount` - Total order value
- `order_status` - Current order status
- `notes` - Order notes
- `ai_agent_interaction_id` - AI interaction reference

#### purchase_order_item
Individual items within purchase orders.
- `id` - Primary key
- `created_at` - Timestamp
- `purchase_order_id` - Foreign key to purchase_order
- `item_name` - Item name
- `quantity` - Quantity ordered
- `unit_price` - Price per unit
- `item_description` - Item description
- `ai_agent_interaction_id` - AI interaction reference

### Marketing & Promotions

#### marketing_campaign
Marketing campaign management.
- `id` - Primary key
- `created_at` - Timestamp
- `campaign_name` - Campaign name
- `campaign_description` - Campaign description
- `start_date` - Campaign start date
- `end_date` - Campaign end date
- `campaign_type` - Type of campaign
- `target_audience` - Target demographic
- `budget` - Planned budget
- `actual_cost` - Actual spend
- `campaign_status` - Current status
- `leads_generated` - Leads generated
- `conversions` - Conversion count
- `revenue_generated` - Revenue attributed
- `roas` - Return on ad spend
- `emails_sent` - Emails sent count
- `emails_opened` - Email open count
- `clicks` - Click count
- `ai_agent_interaction_id` - AI interaction reference
- `communication_channel` - Primary channel used

#### campaing_performance_detail
Detailed campaign performance metrics.
- `id` - Primary key
- `created_at` - Timestamp
- `campaign_id` - Foreign key to marketing_campaign
- `performance_date` - Performance date
- `impressions` - Impression count
- `clicks` - Click count
- `cpc` - Cost per click
- `conversion_rate` - Conversion rate
- `open_rate` - Email open rate
- `click_rate` - Click-through rate
- `bounce_rate` - Email bounce rate
- `unsubscribe_rate` - Unsubscribe rate
- `ai_agent_interaction_id` - AI interaction reference
- `communication_channel` - Channel used

#### promotional_campaign
Promotional campaigns and offers.
- `id` - Primary key
- `created_at` - Timestamp
- `campaign_name` - Campaign name
- `campaign_description` - Campaign description
- `start_date` - Campaign start date
- `end_date` - Campaign end date
- `campaign_type` - Type of promotion
- `target_audience` - Target demographic
- `budget` - Planned budget
- `actual_cost` - Actual cost
- `campaign_status` - Current status
- `leads_generated` - Leads generated
- `conversions` - Conversion count
- `revenue_generated` - Revenue generated
- `roas` - Return on ad spend
- `emails_sent` - Emails sent
- `emails_opened` - Emails opened
- `clicks` - Click count
- `global_data_id` - Global data reference

#### promotional_performance
Promotional campaign performance tracking.
- `id` - Primary key
- `created_at` - Timestamp
- `campaign_id` - Foreign key to promotional_campaign
- `performance_date` - Performance date
- `items_sold` - Items sold through promotion
- `revenue_generated` - Revenue from promotion
- `promotion_cost` - Cost of promotion
- `leads_generated` - Leads generated
- `conversions` - Conversions
- `ai_agent_interaction_id` - AI interaction reference

#### promotional_reward_rule
Rules for promotional rewards and offers.
- `id` - Primary key
- `created_at` - Timestamp
- `campaign_id` - Foreign key to promotional_campaign
- `visits_required` - Required visits for reward
- `reward_type` - Type of reward
- `discount_amount` - Discount value
- `free_item_description` - Free item details
- `applies_to_food_and_beverage` - F&B applicability
- `applies_to_pro_shop` - Pro shop applicability
- `food_and_beverage_category` - Applicable F&B categories
- `pro_shop_category` - Applicable pro shop categories
- `min_months_since_last_visit` - Minimum time since last visit
- `max_months_since_last_visit` - Maximum time since last visit

#### rewards_program
Customer loyalty and rewards program.
- `id` - Primary key
- `created_at` - Timestamp
- `golfer_id` - Foreign key to golfer
- `points_earned` - Points earned
- `points_redeemed` - Points used
- `reward_description` - Reward details
- `redemption_datetime` - When reward was redeemed
- `reward_type` - Type of reward
- `is_promotional` - Promotional reward flag
- `campaign_id` - Associated campaign
- `promo_code` - Promotional code
- `reward_source` - Source of reward
- `ai_agent_interaction_id` - AI interaction reference

### Tournament Management

#### tournament_event
Golf tournaments and events.
- `id` - Primary key
- `created_at` - Timestamp
- `golf_course_id` - Foreign key to golf_course
- `event_name` - Tournament name
- `event_description` - Event description
- `start_datetime` - Tournament start time
- `end_datetime` - Tournament end time
- `location` - Tournament location
- `registration_deadline` - Registration cutoff
- `number_of_participants` - Participant count
- `global_data_id` - Global data reference
- `ai_agent_interaction_id` - AI interaction reference
- `individual_entry_fee` - Individual entry cost
- `team_entry_fee` - Team entry cost
- `tournament_format` - Tournament format
- `prizes` - Prize information
- `entry_fee_includes` - What entry fee covers
- `invitational_only` - Invitation-only flag
- `event_datetime` - Event date and time

#### tournament_enrollment
Tournament registration and enrollment.
- `id` - Primary key
- `created_at` - Timestamp
- `tournament_event_id` - Foreign key to tournament_event
- `golfer_id` - Foreign key to golfer
- `team_name` - Team name (if applicable)
- `payment_processed` - Payment status

### Employee Management

#### employee_timesheet
Employee work schedules.
- `id` - Primary key
- `created_at` - Timestamp
- `employee_id` - Employee identifier
- `week_start_date` - Week starting date
- `shift_start_time` - Shift start time
- `shift_end_time` - Shift end time
- `day_of_week` - Day of the week
- `notes` - Schedule notes
- `golf_course_id` - Foreign key to golf_course
- `global_data_id` - Global data reference
- `ai_agent_interaction_id` - AI interaction reference

#### employee_time_entry
Time clock entries for employees.
- `id` - Primary key
- `created_at` - Timestamp
- `employee_id` - Employee identifier
- `clock_in_time` - Clock in timestamp
- `clock_out_time` - Clock out timestamp
- `entry_date` - Entry date
- `notes` - Time entry notes
- `global_data_id` - Global data reference
- `ai_agent_interaction_id` - AI interaction reference

#### time_off_request
Employee time off requests.
- `id` - Primary key
- `created_at` - Timestamp
- `employee_id` - Employee identifier
- `request_date` - Request submission date
- `start_date` - Time off start date
- `end_date` - Time off end date
- `request_type` - Type of time off
- `status` - Request status
- `employee_notes` - Employee notes
- `approver_notes` - Manager notes
- `approved_by_user_id` - Approving manager
- `approval_date` - Approval date
- `golf_course_id` - Associated course

### Maintenance Management

#### maintenance_task
Course and facility maintenance tasks.
- `id` - Primary key
- `created_at` - Timestamp
- `title` - Task title
- `description` - Task description
- `assigned_to` - Assigned staff member
- `status` - Task status
- `priority` - Task priority level
- `due_date` - Due date
- `location` - Task location
- `equipment_needed` - Required equipment
- `updated_at` - Last update timestamp
- `golf_course_id` - Foreign key to golf_course

### Communication & Notifications

#### notification
System notifications to users.
- `id` - Primary key
- `created_at` - Timestamp
- `user_id` - Foreign key to user
- `notification_type` - Type of notification
- `notification_content` - Notification message
- `is_read` - Read status
- `tee_time_booking_id` - Related tee time booking
- `e_commerce_transaction_id` - Related e-commerce transaction
- `food_and_beverage_order_id` - Related F&B order
- `transaction_id` - Related transaction
- `golfer_id` - Foreign key to golfer
- `golf_course_id` - Associated course

#### golf_news
Golf news and updates.
- `id` - Primary key
- `created_at` - Timestamp
- `news_title` - News headline
- `news_content` - News content
- `publish_datetime` - Publication timestamp
- `news_type` - Type of news
- `golf_course_id` - Associated course
- `player_name` - Featured player name
- `player_position` - Player position/ranking
- `player_score` - Player score
- `player_round_score` - Round score
- `golfer_id` - Associated golfer
- `predicted_winner` - Prediction data
- `prediction_correct` - Prediction accuracy
- `reward_points` - Points awarded

### Analytics & Reporting

#### analytics_report
Business analytics and reporting.
- `id` - Primary key
- `created_at` - Timestamp
- `golf_course_id` - Foreign key to golf_course
- `report_name` - Report name
- `report_description` - Report description
- `report_type` - Type of report
- `report_data` - Report data/results
- `global_data_id` - Global data reference
- `ai_agent_interaction_id` - AI interaction reference
- `is_saved` - Report saved flag
- `saved_by_user_id` - User who saved report
- `is_scheduled` - Scheduled report flag
- `schedule_frequency` - Schedule frequency
- `delivery_method` - Report delivery method
- `delivery_recipient` - Report recipient
- `visualization_type` - Chart/graph type

#### global_data
Aggregated business data and metrics.
- `id` - Primary key
- `created_at` - Timestamp
- `golf_course_id` - Foreign key to golf_course
- `data_date` - Data date
- `total_revenue` - Total revenue
- `tee_times_booked` - Tee times booked count
- `pro_shop_sales` - Pro shop revenue
- `food_and_beverage_sales` - F&B revenue
- `active_members` - Active membership count
- `rewards_points_issued` - Rewards points distributed
- `marketing_performance_data` - Marketing metrics
- `promotional_performance_data` - Promotional metrics
- `accounting_data` - Financial data
- `ai_agent_interaction_id` - AI interaction reference
- `golfer_id` - Associated golfer

#### daily_tee_time_data
Daily tee time analytics.
- `id` - Primary key
- `created_at` - Timestamp
- `tee_time_id` - Foreign key to tee_time
- `ai_agent_interaction_id` - AI interaction reference

#### monthly_tee_time_view
Monthly tee time analytics view.
- `id` - Primary key
- `created_at` - Timestamp
- `month_start_date` - Month start date
- `tee_time` - Tee time
- `course_id` - Course identifier
- `number_of_players` - Player count
- `golfer_id` - Foreign key to golfer
- `golf_course_id` - Foreign key to golf_course
- `ai_agent_interaction_id` - AI interaction reference
- `weekly_tee_time_id` - Weekly view reference

#### weekly_tee_time_view
Weekly tee time analytics view.
- `id` - Primary key
- `created_at` - Timestamp
- `week_start_date` - Week start date
- `tee_time` - Tee time
- `course_id` - Course identifier
- `number_of_players` - Player count
- `number_of_available_tee_times` - Available slots
- `golfer_id` - Foreign key to golfer
- `golf_course_id` - Foreign key to golf_course
- `ai_agent_interaction_id` - AI interaction reference

### System Configuration

#### settings_configuration
System-wide configuration settings.
- `id` - Primary key
- `created_at` - Timestamp
- `golf_course_id` - Foreign key to golf_course
- `setting_name` - Setting name
- `setting_value` - Setting value
- `setting_description` - Setting description
- `global_data_id` - Global data reference
- `ai_agent_interaction_id` - AI interaction reference

#### back_office_setting
Back office configuration settings.
- `id` - Primary key
- `created_at` - Timestamp
- `golf_course_id` - Foreign key to golf_course
- `summary_view` - Summary view configuration
- `setting_value` - Setting value
- `setting_description` - Setting description
- `global_data_id` - Global data reference
- `ai_agent_interaction_id` - AI interaction reference

#### integration
Third-party system integrations.
- `id` - Primary key
- `created_at` - Timestamp
- `product_name` - Integration product name
- `integration_type` - Type of integration
- `api_endpoint` - API endpoint URL
- `api_key` - API authentication key
- `integration_status` - Integration status
- `last_sync_datetime` - Last synchronization time
- `ai_agent_interaction_id` - AI interaction reference

### AI & System Interactions

#### ai_agent_interaction
AI system interactions and queries.
- `id` - Primary key
- `created_at` - Timestamp
- `interaction_timestamp` - Interaction time
- `user_id` - Foreign key to user
- `query` - User query/input
- `response` - AI response
- `module` - System module involved
- `details` - Additional interaction details
- `golf_course_id` - Associated course

### Corporate & Multi-Location Management

#### corp_local_course_level
Corporate hierarchy and multi-location management.
- `id` - Primary key
- `created_at` - Timestamp
- `corp_id` - Corporate entity identifier
- `region_id` - Region identifier
- `state_id` - State identifier
- `metro_id` - Metropolitan area identifier
- `golf_course_id` - Foreign key to golf_course
- `corp_address` - Corporate address
- `corp_contact_email` - Corporate contact email
- `corp_contact_number` - Corporate contact phone

#### roster_member
Staff and member roster management.
- `id` - Primary key
- `created_at` - Timestamp
- `first_name` - First name
- `last_name` - Last name
- `email` - Email address
- `phone_number` - Phone number
- `membership_status` - Membership status
- `golfer_id` - Foreign key to golfer
- `global_data_id` - Global data reference
- `ai_agent_interaction_id` - AI interaction reference

## Key Relationships

### Primary Relationships
- **Golf Courses** are the central entity, with most other entities referencing `golf_course_id`
- **Golfers** are the primary customers, referenced across transactions, bookings, and memberships
- **Users** manage the system and have role-based permissions
- **Transactions** link to various revenue sources (tee times, pro shop, F&B, e-commerce)

### Revenue Streams
1. **Tee Time Bookings** - `tee_time` → `tee_time_booking` → `transaction`
2. **Pro Shop Sales** - `pro_shop_item` → `pro_shop_item_transaction` → `transaction`
3. **Food & Beverage** - `food_and_beverage_item` → `food_and_beverage_item_transaction` → `transaction`
4. **E-Commerce** - `e_commerce_item` → `e_commerce_transaction` → `transaction`
5. **Coaching Services** - `coaching_session` → `transaction`
6. **Tournament Fees** - `tournament_event` → `tournament_enrollment` → `transaction`

### Operational Workflows
- **Booking Process**: `calendar_event` → `tee_time_booking` → `tee_time` → `in_play_mode`
- **Order Management**: `food_and_beverage_item` → `food_and_beverage_order` → `transaction`
- **Inventory Management**: `supplier` → `purchase_order` → `purchase_order_item` → `pro_shop_item`
- **Marketing Campaign**: `marketing_campaign` → `promotional_campaign` → `rewards_program`

### Data Analytics Flow
- Operational data flows into `global_data` for aggregation
- `analytics_report` provides business intelligence
- Time-based views (`daily_tee_time_data`, `weekly_tee_time_view`, `monthly_tee_time_view`) support trend analysis

## Business Rules & Constraints

### Data Integrity
- All monetary amounts should be stored with appropriate precision (typically decimal with 2 decimal places)
- Timestamps should include timezone information for multi-location operations
- Foreign key relationships must be maintained with appropriate cascading rules

### Security Considerations
- Sensitive payment data is encrypted and stored separately in `sensitive_payment_data`
- User passwords should be hashed using secure algorithms
- PII data should be handled according to privacy regulations

### Performance Considerations
- Indexes should be created on frequently queried fields (golf_course_id, golfer_id, created_at)
- Large text fields (descriptions, notes) may benefit from full-text search indexes
- Time-series data may benefit from partitioning strategies

This data model supports a comprehensive golf course management system with integrated CRM, POS, scheduling, coaching, and business intelligence capabilities.